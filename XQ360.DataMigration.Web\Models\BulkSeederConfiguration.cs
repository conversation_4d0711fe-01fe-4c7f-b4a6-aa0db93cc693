using System.ComponentModel.DataAnnotations;

namespace XQ360.DataMigration.Web.Models;

/// <summary>
/// Configuration options for the bulk seeder functionality
/// </summary>
public class BulkSeederConfiguration
{
    public const string SectionName = "BulkSeeder";

    /// <summary>
    /// Default number of drivers to process when not specified
    /// </summary>
    [Range(1, 1000000)]
    public int DefaultDriversCount { get; set; } = 10000;

    /// <summary>
    /// Default number of vehicles to process when not specified
    /// </summary>
    [Range(1, 1000000)]
    public int DefaultVehiclesCount { get; set; } = 5000;

    /// <summary>
    /// Default batch size for bulk operations
    /// </summary>
    [Range(100, 100000)]
    public int DefaultBatchSize { get; set; } = 1000;

    /// <summary>
    /// Maximum allowed batch size
    /// </summary>
    [Range(1000, 100000)]
    public int MaxBatchSize { get; set; } = 50000;

    /// <summary>
    /// Timeout for SqlBulkCopy operations in seconds
    /// </summary>
    [Range(30, 3600)]
    public int BulkCopyTimeout { get; set; } = 300;

    /// <summary>
    /// Timeout for SQL commands in seconds
    /// </summary>
    [Range(30, 1800)]
    public int CommandTimeout { get; set; } = 120;

    /// <summary>
    /// Number of rows after which SqlBulkCopy sends a notification
    /// </summary>
    [Range(100, 10000)]
    public int NotifyAfter { get; set; } = 1000;

    /// <summary>
    /// Enable retry policy for transient failures
    /// </summary>
    public bool EnableRetry { get; set; } = true;

    /// <summary>
    /// Maximum number of retry attempts
    /// </summary>
    [Range(1, 10)]
    public int MaxRetryAttempts { get; set; } = 3;

    /// <summary>
    /// Delay between retry attempts in seconds
    /// </summary>
    [Range(1, 60)]
    public int RetryDelaySeconds { get; set; } = 5;

    /// <summary>
    /// Enable validation of input data before processing
    /// </summary>
    public bool ValidationEnabled { get; set; } = true;

    /// <summary>
    /// Stop processing on first validation error
    /// </summary>
    public bool StopOnFirstError { get; set; } = false;

    /// <summary>
    /// Enable dealer-specific validation and scoping
    /// </summary>
    public bool DealerValidationEnabled { get; set; } = true;

    /// <summary>
    /// Require dealer selection before seeding operations
    /// </summary>
    public bool RequireDealerSelection { get; set; } = true;

    /// <summary>
    /// Default dealer ID to use when not specified (optional)
    /// </summary>
    public Guid? DefaultDealerId { get; set; }

    /// <summary>
    /// Default dealer name to use when not specified (optional)
    /// </summary>
    public string? DefaultDealerName { get; set; }

    /// <summary>
    /// Whether to clean up staging data after successful processing
    /// </summary>
    public bool CleanupStagingData { get; set; } = true;

    /// <summary>
    /// Use temporary tables instead of permanent staging tables
    /// </summary>
    public bool UseTempTables { get; set; } = true;

    /// <summary>
    /// Temporary table mode: SessionScoped or Global
    /// </summary>
    public string TempTableMode { get; set; } = "SessionScoped";

    /// <summary>
    /// Batch size for temporary table operations
    /// </summary>
    [Range(100, 50000)]
    public int TempTableBatchSize { get; set; } = 5000;

    /// <summary>
    /// Create indexes on temporary tables for performance
    /// </summary>
    public bool TempTableIndexes { get; set; } = true;

    /// <summary>
    /// Enable detailed logging of temporary table operations
    /// </summary>
    public bool LogTempTableOperations { get; set; } = true;

    /// <summary>
    /// Connection pool optimization settings for Phase 1.2.3
    /// Dedicated connection pools for staging vs production operations
    /// Min 10, Max 100 connections, 30-second idle timeout
    /// </summary>

    /// <summary>
    /// Minimum connections in the staging operations pool
    /// </summary>
    [Range(1, 50)]
    public int StagingPoolMinConnections { get; set; } = 10;

    /// <summary>
    /// Maximum connections in the staging operations pool
    /// </summary>
    [Range(10, 100)]
    public int StagingPoolMaxConnections { get; set; } = 100;

    /// <summary>
    /// Minimum connections in the production operations pool
    /// </summary>
    [Range(1, 50)]
    public int ProductionPoolMinConnections { get; set; } = 10;

    /// <summary>
    /// Maximum connections in the production operations pool
    /// </summary>
    [Range(10, 100)]
    public int ProductionPoolMaxConnections { get; set; } = 100;

    /// <summary>
    /// Connection idle timeout in seconds before being closed
    /// </summary>
    [Range(10, 300)]
    public int ConnectionIdleTimeoutSeconds { get; set; } = 30;

    /// <summary>
    /// Connection lifetime in seconds before forced renewal
    /// </summary>
    [Range(60, 3600)]
    public int ConnectionLifetimeSeconds { get; set; } = 600;

    /// <summary>
    /// Enable connection pooling optimization
    /// </summary>
    public bool EnableConnectionPooling { get; set; } = true;
}

/// <summary>
/// Options for seeding operation
/// </summary>
public class SeederOptions
{
    public int? DriversCount { get; set; }
    public int? VehiclesCount { get; set; }
    public int? BatchSize { get; set; }
    public bool DryRun { get; set; }
    public bool GenerateData { get; set; }
    public bool Interactive { get; set; } = true;
    public string? DealerId { get; set; }
    public string? CustomerName { get; set; }
}

/// <summary>
/// Result of seeding operation
/// </summary>
public class SeederResult
{
    public bool Success { get; set; }
    public string SessionId { get; set; } = string.Empty;
    public int TotalRows { get; set; }
    public int ProcessedRows { get; set; }
    public int SuccessfulRows { get; set; }
    public int FailedRows { get; set; }
    public TimeSpan Duration { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
    public string Summary { get; set; } = string.Empty;
}

/// <summary>
/// Result of data generation operation
/// </summary>
public class DataGenerationResult
{
    public bool Success { get; set; }
    public int GeneratedRows { get; set; }
    public TimeSpan Duration { get; set; }
    public List<string> Errors { get; set; } = new();
    public string Summary { get; set; } = string.Empty;
}

/// <summary>
/// Result of data validation operation
/// </summary>
public class ValidationResult
{
    public bool Success { get; set; }
    public int ValidRows { get; set; }
    public int InvalidRows { get; set; }
    public List<string> ValidationErrors { get; set; } = new();
    public string Summary { get; set; } = string.Empty;
}

/// <summary>
/// Result of data processing operation
/// </summary>
public class ProcessingResult
{
    public bool Success { get; set; }
    public int ProcessedRows { get; set; }
    public int InsertedRows { get; set; }
    public int UpdatedRows { get; set; }
    public int SkippedRows { get; set; }
    public List<string> ProcessingErrors { get; set; } = new();
    public string Summary { get; set; } = string.Empty;
}

/// <summary>
/// Options for parallel processing operations - Phase 3.1.1
/// </summary>
public class ParallelProcessingOptions
{
    /// <summary>
    /// Enable memory management during processing
    /// </summary>
    public bool EnableMemoryManagement { get; set; } = true;

    /// <summary>
    /// Interval for triggering garbage collection
    /// </summary>
    public int GcCollectionInterval { get; set; } = 1000;

    /// <summary>
    /// Maximum bounded capacity for dataflow operations
    /// </summary>
    public int MaxBoundedCapacity { get; set; } = 10000;

    /// <summary>
    /// Progress callback for tracking processing
    /// </summary>
    public Func<int, int, int, Task>? ProgressCallback { get; set; }
}

/// <summary>
/// Result of parallel processing operation - Phase 3.1.1
/// </summary>
public class ParallelProcessingResult<T>
{
    public bool Success { get; set; }
    public int TotalItems { get; set; }
    public int ProcessedItems { get; set; }
    public int SuccessfulItems { get; set; }
    public int FailedItems { get; set; }
    public List<T> Results { get; set; } = new();
    public List<Exception> Exceptions { get; set; } = new();
    public TimeSpan Duration { get; set; }
    public DateTime StartTime { get; set; }
    public long PeakMemoryUsageMB { get; set; }
}

/// <summary>
/// Performance metrics for parallel processing - Phase 3.1.1
/// </summary>
public class ParallelProcessingMetrics
{
    public int AvailableProcessors { get; set; }
    public int ConfiguredDegreeOfParallelism { get; set; }
    public long CurrentMemoryUsageMB { get; set; }
    public long WorkingSetMB { get; set; }
    public int ThreadPoolThreads { get; set; }
    public int AvailableThreadPoolThreads { get; set; }
}

/// <summary>
/// Options for async optimization operations - Phase 3.1.2
/// </summary>
public class AsyncOptimizationOptions
{
    /// <summary>
    /// Operation timeout in milliseconds
    /// </summary>
    public int TimeoutMs { get; set; } = 30000;

    /// <summary>
    /// Enable retry logic for failed operations
    /// </summary>
    public bool EnableRetry { get; set; } = true;

    /// <summary>
    /// Maximum number of retry attempts
    /// </summary>
    public int MaxRetryAttempts { get; set; } = 3;

    /// <summary>
    /// Base delay between retries in milliseconds
    /// </summary>
    public int RetryDelayMs { get; set; } = 1000;

    /// <summary>
    /// Backoff multiplier for exponential retry delay
    /// </summary>
    public double RetryBackoffMultiplier { get; set; } = 2.0;
}

/// <summary>
/// Result of async optimization operation - Phase 3.1.2
/// </summary>
public class AsyncOptimizationResult
{
    public bool Success { get; set; }
    public object? Result { get; set; }
    public TimeSpan Duration { get; set; }
    public DateTime StartTime { get; set; }
    public string? ErrorMessage { get; set; }
    public Exception? Exception { get; set; }
    public bool WasCancelled { get; set; }
    public bool WasTimedOut { get; set; }
}

/// <summary>
/// Options for async stream processing - Phase 3.1.2
/// </summary>
public class AsyncStreamProcessingOptions
{
    /// <summary>
    /// Buffer size for stream processing
    /// </summary>
    public int BufferSize { get; set; } = 1000;

    /// <summary>
    /// Process items in parallel within buffer
    /// </summary>
    public bool ProcessInParallel { get; set; } = true;

    /// <summary>
    /// Flush buffer immediately rather than waiting for full buffer
    /// </summary>
    public bool FlushImmediately { get; set; } = false;

    /// <summary>
    /// Enable memory optimization during processing
    /// </summary>
    public bool EnableMemoryOptimization { get; set; } = true;

    /// <summary>
    /// Interval for garbage collection
    /// </summary>
    public int GcInterval { get; set; } = 5000;

    /// <summary>
    /// Progress callback for stream processing
    /// </summary>
    public Func<int, int, Task>? ProgressCallback { get; set; }
}

/// <summary>
/// Result of async stream processing - Phase 3.1.2
/// </summary>
public class AsyncStreamProcessingResult<T>
{
    public bool Success { get; set; }
    public List<T> Results { get; set; } = new();
    public int ProcessedCount { get; set; }
    public TimeSpan Duration { get; set; }
    public DateTime StartTime { get; set; }
    public string? ErrorMessage { get; set; }
    public Exception? Exception { get; set; }
}

/// <summary>
/// Result of cancellation token optimization - Phase 3.1.2
/// </summary>
public class CancellationTokenOptimizationResult
{
    public bool Success { get; set; }
    public TimeSpan Duration { get; set; }
    public DateTime StartTime { get; set; }
    public bool InitialTokenCanBeCanceled { get; set; }
    public bool InitialTokenIsCanceled { get; set; }
    public bool FinalTokenIsCanceled { get; set; }
    public bool WasCancelled { get; set; }
    public string? CancellationReason { get; set; }
    public string? ErrorMessage { get; set; }
    public Exception? Exception { get; set; }
}

/// <summary>
/// Result of thread pool optimization - Phase 3.1.2
/// </summary>
public class ThreadPoolOptimizationResult
{
    public bool Success { get; set; }
    public DateTime StartTime { get; set; }
    public int InitialMinWorkerThreads { get; set; }
    public int InitialMaxWorkerThreads { get; set; }
    public int InitialAvailableWorkerThreads { get; set; }
    public int OptimizedMinWorkerThreads { get; set; }
    public int OptimizedMaxWorkerThreads { get; set; }
    public int OptimizedAvailableWorkerThreads { get; set; }
    public bool SettingsApplied { get; set; }
    public string? ErrorMessage { get; set; }
    public Exception? Exception { get; set; }
}

/// <summary>
/// Interface for memory-optimizable options
/// </summary>
public interface IMemoryOptimizable
{
    /// <summary>
    /// Enable memory management
    /// </summary>
    bool EnableMemoryManagement { get; set; }

    /// <summary>
    /// Garbage collection interval
    /// </summary>
    int GcInterval { get; set; }
}

/// <summary>
/// Options for streaming operations - Phase 3.1.3
/// </summary>
public class StreamingOptions : IMemoryOptimizable
{
    /// <summary>
    /// Command timeout for streaming queries in seconds
    /// </summary>
    public int CommandTimeoutSeconds { get; set; } = 300;

    /// <summary>
    /// Enable memory management during streaming
    /// </summary>
    public bool EnableMemoryManagement { get; set; } = true;

    /// <summary>
    /// Interval for garbage collection
    /// </summary>
    public int GcInterval { get; set; } = 10000;

    /// <summary>
    /// Interval for progress reporting
    /// </summary>
    public int ProgressReportInterval { get; set; } = 1000;

    /// <summary>
    /// Interval for yielding control to other tasks
    /// </summary>
    public int YieldInterval { get; set; } = 100;

    /// <summary>
    /// Stop streaming on mapping errors
    /// </summary>
    public bool StopOnMappingError { get; set; } = false;

    /// <summary>
    /// Use SQL hints for streaming optimization
    /// </summary>
    public bool UseStreamingHints { get; set; } = true;

    /// <summary>
    /// FAST hint value for SQL queries
    /// </summary>
    public int FastRowsHint { get; set; } = 1000;

    /// <summary>
    /// Progress callback for streaming operations
    /// </summary>
    public Func<int, TimeSpan, Task>? ProgressCallback { get; set; }
}

/// <summary>
/// Options for streaming transformation - Phase 3.1.3
/// </summary>
public class StreamingTransformOptions : IMemoryOptimizable
{
    /// <summary>
    /// Buffer size for batching transformations
    /// </summary>
    public int BufferSize { get; set; } = 1000;

    /// <summary>
    /// Process transformations in parallel
    /// </summary>
    public bool ProcessInParallel { get; set; } = true;

    /// <summary>
    /// Process items immediately instead of buffering
    /// </summary>
    public bool ProcessImmediately { get; set; } = false;

    /// <summary>
    /// Enable memory management
    /// </summary>
    public bool EnableMemoryManagement { get; set; } = true;

    /// <summary>
    /// Garbage collection interval
    /// </summary>
    public int GcInterval { get; set; } = 5000;
}

/// <summary>
/// Options for streaming batch processing - Phase 3.1.3
/// </summary>
public class StreamingBatchOptions : IMemoryOptimizable
{
    /// <summary>
    /// Enable memory management
    /// </summary>
    public bool EnableMemoryManagement { get; set; } = true;

    /// <summary>
    /// Garbage collection interval (in number of batches)
    /// </summary>
    public int GcInterval { get; set; } = 10;

    /// <summary>
    /// Progress callback for batch processing
    /// </summary>
    public Func<int, int, TimeSpan, Task>? ProgressCallback { get; set; }
}

/// <summary>
/// Options for memory pressure monitoring - Phase 3.1.3
/// </summary>
public class MemoryPressureOptions : IMemoryOptimizable
{
    /// <summary>
    /// Memory threshold in MB to trigger optimization
    /// </summary>
    public long MemoryThresholdMB { get; set; } = 1000;

    /// <summary>
    /// Pause streaming when memory is too high
    /// </summary>
    public bool PauseOnHighMemory { get; set; } = true;

    /// <summary>
    /// Delay in milliseconds when pausing due to memory pressure
    /// </summary>
    public int MemoryPauseDelayMs { get; set; } = 1000;

    /// <summary>
    /// Interval for periodic garbage collection in milliseconds
    /// </summary>
    public int PeriodicGcIntervalMs { get; set; } = 30000;

    /// <summary>
    /// Yield interval for giving control to other tasks
    /// </summary>
    public int YieldInterval { get; set; } = 100;

    /// <summary>
    /// Enable memory management
    /// </summary>
    public bool EnableMemoryManagement { get; set; } = true;
}

/// <summary>
/// Streaming performance metrics - Phase 3.1.3
/// </summary>
public class StreamingMetrics
{
    public long CurrentMemoryUsageMB { get; set; }
    public long WorkingSetMB { get; set; }
    public int Gen0Collections { get; set; }
    public int Gen1Collections { get; set; }
    public int Gen2Collections { get; set; }
    public long AvailableMemoryMB { get; set; }
    public int ThreadCount { get; set; }
    public DateTime Timestamp { get; set; }
}

/// <summary>
/// Options for indexing strategy - Phase 3.2.1
/// </summary>
public class IndexingStrategyOptions
{
    /// <summary>
    /// Create dynamic indexes based on usage patterns
    /// </summary>
    public bool CreateDynamicIndexes { get; set; } = true;

    /// <summary>
    /// Update statistics after index creation
    /// </summary>
    public bool UpdateStatistics { get; set; } = true;

    /// <summary>
    /// Analyze index usage after creation
    /// </summary>
    public bool AnalyzeIndexUsage { get; set; } = true;

    /// <summary>
    /// Create session-specific indexes for staging tables
    /// </summary>
    public bool CreateSessionSpecificIndexes { get; set; } = true;

    /// <summary>
    /// Create date-based indexes for time-series data
    /// </summary>
    public bool CreateDateBasedIndexes { get; set; } = false;

    /// <summary>
    /// Use full scan for statistics updates
    /// </summary>
    public bool UseFullScanForStatistics { get; set; } = false;
}

/// <summary>
/// Result of indexing optimization - Phase 3.2.1
/// </summary>
public class IndexingOptimizationResult
{
    public bool Success { get; set; }
    public DateTime StartTime { get; set; }
    public TimeSpan Duration { get; set; }
    public int IndexesCreated { get; set; }
    public int StatisticsUpdated { get; set; }
    public List<string> ExecutionErrors { get; set; } = new();
    public List<IndexUsageStatistic> IndexUsageAnalysis { get; set; } = new();
    public string? ErrorMessage { get; set; }
    public Exception? Exception { get; set; }
}

/// <summary>
/// Options for index maintenance - Phase 3.2.1
/// </summary>
public class IndexMaintenanceOptions
{
    /// <summary>
    /// Fragmentation threshold for triggering maintenance
    /// </summary>
    public double FragmentationThreshold { get; set; } = 10.0;

    /// <summary>
    /// Fragmentation threshold for rebuilding instead of reorganizing
    /// </summary>
    public double RebuildThreshold { get; set; } = 30.0;

    /// <summary>
    /// Update statistics after maintenance operations
    /// </summary>
    public bool UpdateStatisticsAfterMaintenance { get; set; } = true;

    /// <summary>
    /// Maximum time to spend on maintenance operations in minutes
    /// </summary>
    public int MaxMaintenanceTimeMinutes { get; set; } = 60;
}

/// <summary>
/// Result of index maintenance - Phase 3.2.1
/// </summary>
public class IndexMaintenanceResult
{
    public bool Success { get; set; }
    public DateTime StartTime { get; set; }
    public TimeSpan Duration { get; set; }
    public int IndexesRebuilt { get; set; }
    public int IndexesReorganized { get; set; }
    public int StatisticsUpdated { get; set; }
    public List<IndexFragmentationInfo> FragmentationAnalysis { get; set; } = new();
    public List<string> MaintenanceErrors { get; set; } = new();
    public string? ErrorMessage { get; set; }
    public Exception? Exception { get; set; }
}

/// <summary>
/// Index usage statistics - Phase 3.2.1
/// </summary>
public class IndexUsageStatistic
{
    public string TableName { get; set; } = string.Empty;
    public string IndexName { get; set; } = string.Empty;
    public string IndexType { get; set; } = string.Empty;
    public long UserSeeks { get; set; }
    public long UserScans { get; set; }
    public long UserLookups { get; set; }
    public long UserUpdates { get; set; }
    public DateTime? LastUserSeek { get; set; }
    public DateTime? LastUserScan { get; set; }
    public DateTime? LastUserLookup { get; set; }
    public DateTime? LastUserUpdate { get; set; }

    public long TotalReads => UserSeeks + UserScans + UserLookups;
    public string UsageStatus => TotalReads == 0 ? "UNUSED" :
                                UserUpdates > TotalReads * 5 ? "HIGH_MAINTENANCE" : "ACTIVE";
}

/// <summary>
/// Index fragmentation information - Phase 3.2.1
/// </summary>
public class IndexFragmentationInfo
{
    public string TableName { get; set; } = string.Empty;
    public string IndexName { get; set; } = string.Empty;
    public string IndexType { get; set; } = string.Empty;
    public double FragmentationPercent { get; set; }
    public long PageCount { get; set; }
    public string Recommendation { get; set; } = string.Empty;
}

/// <summary>
/// Index optimization suggestions - Phase 3.2.1
/// </summary>
public class IndexOptimizationSuggestions
{
    public List<string> MissingIndexes { get; set; } = new();
    public List<string> UnusedIndexes { get; set; } = new();
    public List<string> DuplicateIndexes { get; set; } = new();
    public List<string> PerformanceRecommendations { get; set; } = new();
}

/// <summary>
/// Options for MERGE operations - Phase 3.2.2
/// </summary>
public class MergeOperationOptions
{
    /// <summary>
    /// Batch size for MERGE operations
    /// </summary>
    public int BatchSize { get; set; } = 1000;

    /// <summary>
    /// Command timeout in seconds
    /// </summary>
    public int CommandTimeoutSeconds { get; set; } = 600;

    /// <summary>
    /// Enable parallel execution plans
    /// </summary>
    public bool EnableParallelExecution { get; set; } = true;

    /// <summary>
    /// Use OUTPUT clause for tracking merged records
    /// </summary>
    public bool UseOutputClause { get; set; } = true;
}

/// <summary>
/// Result of MERGE operation - Phase 3.2.2
/// </summary>
public class MergeOperationResult
{
    public bool Success { get; set; }
    public Guid SessionId { get; set; }
    public string EntityType { get; set; } = string.Empty;
    public DateTime StartTime { get; set; }
    public TimeSpan Duration { get; set; }
    public int ProcessedRecords { get; set; }
    public int DurationMs { get; set; }
    public decimal RecordsPerSecond { get; set; }
    public string? ErrorMessage { get; set; }
    public Exception? Exception { get; set; }
}

/// <summary>
/// Options for comprehensive MERGE operations - Phase 3.2.2
/// </summary>
public class ComprehensiveMergeOptions
{
    /// <summary>
    /// Batch size for MERGE operations
    /// </summary>
    public int BatchSize { get; set; } = 1000;

    /// <summary>
    /// Command timeout in seconds
    /// </summary>
    public int CommandTimeoutSeconds { get; set; } = 600;

    /// <summary>
    /// Enable parallel execution plans
    /// </summary>
    public bool EnableParallelExecution { get; set; } = true;

    /// <summary>
    /// MERGE persons and drivers
    /// </summary>
    public bool MergePersonsDrivers { get; set; } = true;

    /// <summary>
    /// MERGE vehicles
    /// </summary>
    public bool MergeVehicles { get; set; } = true;

    /// <summary>
    /// MERGE cards and access permissions
    /// </summary>
    public bool MergeCardAccess { get; set; } = true;

    /// <summary>
    /// Stop on first failure
    /// </summary>
    public bool StopOnFirstFailure { get; set; } = true;
}

/// <summary>
/// Result of comprehensive MERGE operation - Phase 3.2.2
/// </summary>
public class ComprehensiveMergeResult
{
    public bool Success { get; set; }
    public Guid SessionId { get; set; }
    public DateTime StartTime { get; set; }
    public TimeSpan Duration { get; set; }
    public int TotalProcessedRecords { get; set; }
    public MergeOperationResult? PersonDriverResult { get; set; }
    public MergeOperationResult? VehicleResult { get; set; }
    public MergeOperationResult? CardAccessResult { get; set; }
    public List<SessionStatistic> SessionStatistics { get; set; } = new();
    public string? ErrorMessage { get; set; }
    public Exception? Exception { get; set; }
}

/// <summary>
/// MERGE performance metrics - Phase 3.2.2
/// </summary>
public class MergePerformanceMetric
{
    public int SessionId { get; set; }
    public DateTime StartTime { get; set; }
    public long TotalElapsedTimeMs { get; set; }
    public long CpuTimeMs { get; set; }
    public long LogicalReads { get; set; }
    public long Writes { get; set; }
    public long RowCount { get; set; }
    public string QueryTextPreview { get; set; } = string.Empty;
}

/// <summary>
/// Session statistics for MERGE operations - Phase 3.2.2
/// </summary>
public class SessionStatistic
{
    public string EntityType { get; set; } = string.Empty;
    public int TotalRecords { get; set; }
    public int ProcessedRecords { get; set; }
    public int PendingRecords { get; set; }
    public int FailedRecords { get; set; }
    public int AvgProcessingTimeMs { get; set; }

    public double ProcessingPercentage => TotalRecords > 0 ? (double)ProcessedRecords / TotalRecords * 100 : 0;
    public double FailurePercentage => TotalRecords > 0 ? (double)FailedRecords / TotalRecords * 100 : 0;
}

/// <summary>
/// Options for performance optimization - Phase 3.2.2
/// </summary>
public class OptimizationOptions
{
    /// <summary>
    /// Update statistics on tables
    /// </summary>
    public bool UpdateStatistics { get; set; } = true;

    /// <summary>
    /// Rebuild fragmented indexes
    /// </summary>
    public bool RebuildFragmentedIndexes { get; set; } = true;

    /// <summary>
    /// Fragmentation threshold for rebuilding indexes
    /// </summary>
    public double FragmentationThreshold { get; set; } = 10.0;

    /// <summary>
    /// Optimize query plans
    /// </summary>
    public bool OptimizeQueryPlans { get; set; } = true;
}

/// <summary>
/// Options for table partitioning - Phase 3.2.3
/// </summary>
public class PartitioningOptions
{
    /// <summary>
    /// Number of partitions to create
    /// </summary>
    public int PartitionCount { get; set; } = 16;

    /// <summary>
    /// Create partition-aligned indexes
    /// </summary>
    public bool CreatePartitionAlignedIndexes { get; set; } = true;

    /// <summary>
    /// Enable automatic partition management
    /// </summary>
    public bool EnableAutomaticManagement { get; set; } = true;
}

/// <summary>
/// Result of partitioning initialization - Phase 3.2.3
/// </summary>
public class PartitioningResult
{
    public bool Success { get; set; }
    public DateTime StartTime { get; set; }
    public TimeSpan Duration { get; set; }
    public bool PartitionFunctionCreated { get; set; }
    public bool PartitionSchemeCreated { get; set; }
    public int PartitionedTablesCreated { get; set; }
    public int PartitionAlignedIndexesCreated { get; set; }
    public List<PartitionInfo> PartitioningInfo { get; set; } = new();
    public List<string> ExecutionErrors { get; set; } = new();
    public string? ErrorMessage { get; set; }
    public Exception? Exception { get; set; }
}

/// <summary>
/// Partition information - Phase 3.2.3
/// </summary>
public class PartitionInfo
{
    public string TableName { get; set; } = string.Empty;
    public int PartitionNumber { get; set; }
    public long RowCount { get; set; }
    public string FileGroupName { get; set; } = string.Empty;
    public Guid? LowerBoundary { get; set; }
    public Guid? UpperBoundary { get; set; }
}

/// <summary>
/// Options for partition cleanup - Phase 3.2.3
/// </summary>
public class PartitionCleanupOptions
{
    /// <summary>
    /// Specific session to cleanup (null for time-based cleanup)
    /// </summary>
    public Guid? SessionId { get; set; }

    /// <summary>
    /// Cleanup sessions older than specified hours
    /// </summary>
    public int CleanupOlderThanHours { get; set; } = 24;

    /// <summary>
    /// Perform dry run without actual cleanup
    /// </summary>
    public bool DryRun { get; set; } = false;

    /// <summary>
    /// Command timeout in seconds
    /// </summary>
    public int CommandTimeoutSeconds { get; set; } = 300;
}

/// <summary>
/// Result of partition cleanup - Phase 3.2.3
/// </summary>
public class PartitionCleanupResult
{
    public bool Success { get; set; }
    public DateTime StartTime { get; set; }
    public TimeSpan Duration { get; set; }
    public bool CleanupExecuted { get; set; }
    public int TotalRecordsToDelete { get; set; }
    public List<PartitionCleanupItem> DryRunResults { get; set; } = new();
    public string? ErrorMessage { get; set; }
    public Exception? Exception { get; set; }
}

/// <summary>
/// Partition cleanup item - Phase 3.2.3
/// </summary>
public class PartitionCleanupItem
{
    public string TableName { get; set; } = string.Empty;
    public int RecordsToDelete { get; set; }
}

/// <summary>
/// Result of partition switch operation - Phase 3.2.3
/// </summary>
public class PartitionSwitchResult
{
    public bool Success { get; set; }
    public Guid SessionId { get; set; }
    public string TableName { get; set; } = string.Empty;
    public DateTime StartTime { get; set; }
    public TimeSpan Duration { get; set; }
    public int PartitionNumber { get; set; }
    public int RecordsSwitched { get; set; }
    public string? ErrorMessage { get; set; }
    public Exception? Exception { get; set; }
}

/// <summary>
/// Partition usage statistics - Phase 3.2.3
/// </summary>
public class PartitionUsageStatistic
{
    public string TableName { get; set; } = string.Empty;
    public int PartitionNumber { get; set; }
    public long RowCount { get; set; }
    public decimal ApproxSizeMB { get; set; }
    public string UsageLevel { get; set; } = string.Empty;
    public string IndexName { get; set; } = string.Empty;
    public string IndexType { get; set; } = string.Empty;
}

/// <summary>
/// Options for partition optimization - Phase 3.2.3
/// </summary>
public class PartitionOptimizationOptions
{
    /// <summary>
    /// Update partition statistics
    /// </summary>
    public bool UpdatePartitionStatistics { get; set; } = true;

    /// <summary>
    /// Rebalance partitions for better distribution
    /// </summary>
    public bool RebalancePartitions { get; set; } = false;

    /// <summary>
    /// Cleanup empty partitions
    /// </summary>
    public bool CleanupEmptyPartitions { get; set; } = true;
}

/// <summary>
/// Result of partition optimization - Phase 3.2.3
/// </summary>
public class PartitionOptimizationResult
{
    public bool Success { get; set; }
    public DateTime StartTime { get; set; }
    public TimeSpan Duration { get; set; }
    public int StatisticsUpdated { get; set; }
    public int PartitionsRebalanced { get; set; }
    public int EmptyPartitionsCleaned { get; set; }
    public List<string> OptimizationErrors { get; set; } = new();
    public string? ErrorMessage { get; set; }
    public Exception? Exception { get; set; }
}