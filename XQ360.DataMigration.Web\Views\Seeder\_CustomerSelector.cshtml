@model XQ360.DataMigration.Web.Models.BulkSeederViewModel

<div class="customer-selector">
    <!-- Customer Selection Mode -->
    <div id="customer-selection-mode">
        <div class="mb-3">
            <label for="customer-select" class="form-label">Customer *</label>
            <select id="customer-select" class="form-select">
                <option value="">Select a customer...</option>
                <option value="new">+ Create New Customer</option>
            </select>
            
            <div id="customer-validation-error" class="invalid-feedback" style="display: none;"></div>
            
            <div id="customer-loading" class="form-text" style="display: none;">
                <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                Loading customers...
            </div>
        </div>

        <!-- Dealer Required Message -->
        <div id="dealer-required-message" class="alert alert-warning">
            <div class="d-flex align-items-center">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <div>
                    <strong>Dealer selection required</strong>
                    <br>
                    <small>Please select a dealer first to load available customers</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Selected Customer Display -->
    <div id="selected-customer" class="card border-success" style="display: none;">
        <div class="card-header bg-success text-white">
            <h6 class="card-title mb-0">
                <i class="fas fa-users me-2"></i>
                Selected Customer
            </h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-8">
                    <div class="customer-info">
                        <div class="fw-bold" id="customer-name"></div>
                        <div class="text-muted small" id="customer-contact"></div>
                    </div>
                </div>
                <div class="col-md-4 text-end">
                    <span class="badge bg-success">
                        <i class="fas fa-check me-1"></i>
                        Active
                    </span>
                </div>
            </div>
            
            <div class="customer-actions mt-2 pt-2 border-top">
                <button type="button" 
                        class="btn btn-outline-secondary btn-sm"
                        onclick="clearCustomerSelection()">
                    <i class="fas fa-times me-1"></i>
                    Change Customer
                </button>
            </div>
        </div>
    </div>

    <!-- Create New Customer Form -->
    <div id="create-customer-form" class="card border-primary" style="display: none;">
        <div class="card-header bg-primary text-white">
            <h6 class="card-title mb-0">
                <i class="fas fa-plus me-2"></i>
                Create New Customer
            </h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="new-customer-name" class="form-label">Company Name *</label>
                        <input type="text" class="form-control" id="new-customer-name" name="newCustomer.name" required>
                        <div class="invalid-feedback"></div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="new-customer-contact-name" class="form-label">Contact Name</label>
                        <input type="text" class="form-control" id="new-customer-contact-name" name="newCustomer.contactName">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="new-customer-email" class="form-label">Contact Email</label>
                        <input type="email" class="form-control" id="new-customer-email" name="newCustomer.contactEmail">
                        <div class="invalid-feedback"></div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="new-customer-phone" class="form-label">Contact Phone</label>
                        <input type="tel" class="form-control" id="new-customer-phone" name="newCustomer.contactPhone">
                        <div class="invalid-feedback"></div>
                    </div>
                </div>
            </div>

            <!-- Address Section -->
            <div class="row">
                <div class="col-12">
                    <h6 class="mb-3">Address Information <small class="text-muted">(Optional)</small></h6>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="new-customer-street" class="form-label">Street Address</label>
                        <input type="text" class="form-control" id="new-customer-street" name="newCustomer.address.street">
                    </div>
                    
                    <div class="mb-3">
                        <label for="new-customer-city" class="form-label">City</label>
                        <input type="text" class="form-control" id="new-customer-city" name="newCustomer.address.city">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="new-customer-state" class="form-label">State/Province</label>
                        <input type="text" class="form-control" id="new-customer-state" name="newCustomer.address.state">
                    </div>
                    
                    <div class="row">
                        <div class="col-6">
                            <div class="mb-3">
                                <label for="new-customer-postal" class="form-label">Postal Code</label>
                                <input type="text" class="form-control" id="new-customer-postal" name="newCustomer.address.postalCode">
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="mb-3">
                                <label for="new-customer-country" class="form-label">Country</label>
                                <input type="text" class="form-control" id="new-customer-country" name="newCustomer.address.country" value="United States">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="customer-form-actions">
                <button type="button" class="btn btn-primary" onclick="createCustomer()">
                    <i class="fas fa-save me-1"></i>
                    <span id="create-customer-btn-text">Create Customer</span>
                </button>
                <button type="button" class="btn btn-outline-secondary ms-2" onclick="cancelCreateCustomer()">
                    <i class="fas fa-times me-1"></i>
                    Cancel
                </button>
            </div>
        </div>
    </div>

    <!-- Error Message -->
    <div id="customer-error" class="alert alert-danger" style="display: none;">
        <div class="d-flex align-items-center">
            <i class="fas fa-exclamation-circle me-2"></i>
            <div>
                <strong>Error with customer operation</strong>
                <br>
                <small id="customer-error-message">An error occurred</small>
            </div>
        </div>
    </div>

    <!-- Hidden inputs to store selected customer -->
    <input type="hidden" id="selected-customer-id" name="SelectedCustomer.Id" value="@Model.SelectedCustomer?.Id" />
    <input type="hidden" id="selected-customer-name" name="SelectedCustomer.Name" value="@Model.SelectedCustomer?.Name" />
    <input type="hidden" id="selected-customer-dealer-id" name="SelectedCustomer.DealerId" value="@Model.SelectedCustomer?.DealerId" />
</div>

<script>
    let selectedCustomer = @Html.Raw(Model.SelectedCustomer != null ? 
        $"{{id: '{Model.SelectedCustomer.Id}', name: '{Model.SelectedCustomer.Name}', dealerId: '{Model.SelectedCustomer.DealerId}', contactName: '{Model.SelectedCustomer.ContactName}', contactEmail: '{Model.SelectedCustomer.ContactEmail}'}}" : 
        "null");

    $(document).ready(function() {
        // Initialize with existing selection if available
        if (selectedCustomer) {
            displaySelectedCustomer(selectedCustomer);
        }

        // Handle customer selection change
        $('#customer-select').on('change', function() {
            const value = $(this).val();
            
            if (value === 'new') {
                showCreateCustomerForm();
            } else if (value && value !== '') {
                selectCustomerById(value);
            } else {
                clearCustomerSelection();
            }
        });

        // Real-time validation for create customer form
        $('#new-customer-name').on('input', validateCustomerForm);
        $('#new-customer-email').on('input', validateCustomerForm);
        $('#new-customer-phone').on('input', validateCustomerForm);
    });

    function loadCustomersForDealer(dealerId) {
        if (!dealerId) {
            $('#dealer-required-message').show();
            $('#customer-selection-mode').hide();
            return;
        }

        $('#dealer-required-message').hide();
        $('#customer-selection-mode').show();
        showCustomerLoading();
        hideCustomerError();

        $.ajax({
            url: '/api/customers/by-dealer/' + dealerId,
            method: 'GET',
            success: function(customers) {
                hideCustomerLoading();
                populateCustomerDropdown(customers);
            },
            error: function(xhr, status, error) {
                hideCustomerLoading();
                showCustomerError('Failed to load customers: ' + (xhr.responseJSON?.message || error));
            }
        });
    }

    function populateCustomerDropdown(customers) {
        const select = $('#customer-select');
        select.html('<option value="">Select a customer...</option>');
        
        customers.forEach(customer => {
            select.append(`<option value="${customer.id}">${customer.name}</option>`);
        });
        
        select.append('<option value="new">+ Create New Customer</option>');
    }

    function selectCustomerById(customerId) {
        // Find customer in the dropdown or fetch from API
        $.ajax({
            url: '/api/customers/' + customerId,
            method: 'GET',
            success: function(customer) {
                selectCustomer(customer);
            },
            error: function(xhr, status, error) {
                showCustomerError('Failed to load customer details: ' + (xhr.responseJSON?.message || error));
            }
        });
    }

    function selectCustomer(customer) {
        selectedCustomer = customer;
        displaySelectedCustomer(customer);
        
        // Update hidden inputs
        $('#selected-customer-id').val(customer.id);
        $('#selected-customer-name').val(customer.name);
        $('#selected-customer-dealer-id').val(customer.dealerId);
        
        // Trigger validation update
        updateFieldValidation('customer', true);
    }

    function displaySelectedCustomer(customer) {
        $('#customer-name').text(customer.name);
        
        let contactInfo = '';
        if (customer.contactName) {
            contactInfo = customer.contactName;
            if (customer.contactEmail) {
                contactInfo += ' (' + customer.contactEmail + ')';
            }
        } else if (customer.contactEmail) {
            contactInfo = customer.contactEmail;
        }
        
        $('#customer-contact').text(contactInfo || 'No contact information');
        $('#selected-customer').show();
        $('#customer-selection-mode').hide();
        $('#create-customer-form').hide();
    }

    function clearCustomerSelection() {
        selectedCustomer = null;
        $('#selected-customer').hide();
        $('#customer-selection-mode').show();
        $('#create-customer-form').hide();
        $('#customer-select').val('');
        
        // Clear hidden inputs
        $('#selected-customer-id').val('');
        $('#selected-customer-name').val('');
        $('#selected-customer-dealer-id').val('');
        
        // Clear validation
        updateFieldValidation('customer', false);
    }

    function showCreateCustomerForm() {
        $('#customer-selection-mode').hide();
        $('#create-customer-form').show();
        $('#new-customer-name').focus();
    }

    function cancelCreateCustomer() {
        $('#create-customer-form').hide();
        $('#customer-selection-mode').show();
        $('#customer-select').val('');
        clearCustomerForm();
    }

    function clearCustomerForm() {
        $('#create-customer-form input').val('');
        $('#new-customer-country').val('United States');
        $('#create-customer-form .is-invalid').removeClass('is-invalid');
    }

    function validateCustomerForm() {
        const name = $('#new-customer-name').val().trim();
        const email = $('#new-customer-email').val().trim();
        const phone = $('#new-customer-phone').val().trim();
        
        let isValid = true;
        
        // Validate name
        if (!name) {
            $('#new-customer-name').addClass('is-invalid').siblings('.invalid-feedback').text('Company name is required');
            isValid = false;
        } else {
            $('#new-customer-name').removeClass('is-invalid');
        }
        
        // Validate email if provided
        if (email && !isValidEmail(email)) {
            $('#new-customer-email').addClass('is-invalid').siblings('.invalid-feedback').text('Please enter a valid email address');
            isValid = false;
        } else {
            $('#new-customer-email').removeClass('is-invalid');
        }
        
        // Validate phone if provided
        if (phone && !isValidPhone(phone)) {
            $('#new-customer-phone').addClass('is-invalid').siblings('.invalid-feedback').text('Please enter a valid phone number');
            isValid = false;
        } else {
            $('#new-customer-phone').removeClass('is-invalid');
        }
        
        return isValid;
    }

    function createCustomer() {
        if (!validateCustomerForm()) {
            return;
        }

        const customerData = {
            dealerId: $('#selected-dealer-id').val(),
            name: $('#new-customer-name').val().trim(),
            contactName: $('#new-customer-contact-name').val().trim() || null,
            contactEmail: $('#new-customer-email').val().trim() || null,
            contactPhone: $('#new-customer-phone').val().trim() || null,
            address: {
                street: $('#new-customer-street').val().trim() || null,
                city: $('#new-customer-city').val().trim() || null,
                state: $('#new-customer-state').val().trim() || null,
                postalCode: $('#new-customer-postal').val().trim() || null,
                country: $('#new-customer-country').val().trim() || null
            }
        };

        // Show loading state
        $('#create-customer-btn-text').text('Creating...');
        $('.customer-form-actions button').prop('disabled', true);

        $.ajax({
            url: '/api/customers',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(customerData),
            success: function(customer) {
                selectCustomer(customer);
                clearCustomerForm();
                showSuccessMessage('Customer created successfully!');
            },
            error: function(xhr, status, error) {
                const message = xhr.responseJSON?.message || error;
                showCustomerError('Failed to create customer: ' + message);
            },
            complete: function() {
                // Reset loading state
                $('#create-customer-btn-text').text('Create Customer');
                $('.customer-form-actions button').prop('disabled', false);
            }
        });
    }

    function showCustomerLoading() {
        $('#customer-loading').show();
    }

    function hideCustomerLoading() {
        $('#customer-loading').hide();
    }

    function showCustomerError(message) {
        $('#customer-error-message').text(message);
        $('#customer-error').show();
    }

    function hideCustomerError() {
        $('#customer-error').hide();
    }

    function isValidEmail(email) {
        const emailRegex = /^[^\s@@]+@@[^\s@@]+\.[^\s@@]+$/;
        return emailRegex.test(email);
    }

    function isValidPhone(phone) {
        const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
        return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
    }
</script>
