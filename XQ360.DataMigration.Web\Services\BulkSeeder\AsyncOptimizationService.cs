using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Diagnostics;
using XQ360.DataMigration.Web.Models;

namespace XQ360.DataMigration.Web.Services.BulkSeeder;

/// <summary>
/// Async/await optimization service for Phase 3.1.2: Async/Await Optimization
/// Implements ConfigureAwait(false) throughout and proper CancellationToken propagation
/// </summary>
public class AsyncOptimizationService : IAsyncOptimizationService
{
    private readonly ILogger<AsyncOptimizationService> _logger;
    private readonly BulkSeederConfiguration _config;

    public AsyncOptimizationService(
        ILogger<AsyncOptimizationService> logger,
        IOptions<BulkSeederConfiguration> config)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _config = config.Value ?? throw new ArgumentNullException(nameof(config));
    }

    public async Task<AsyncOptimizationResult> OptimizeAsyncOperationAsync<T>(
        Func<CancellationToken, Task<T>> operation,
        AsyncOptimizationOptions options,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var result = new AsyncOptimizationResult
        {
            StartTime = DateTime.UtcNow
        };

        _logger.LogDebug("Starting async operation optimization with timeout: {TimeoutMs}ms", 
            options.TimeoutMs);

        try
        {
            // Create combined cancellation token with timeout
            using var timeoutCts = new CancellationTokenSource(TimeSpan.FromMilliseconds(options.TimeoutMs));
            using var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(
                cancellationToken, timeoutCts.Token);

            T operationResult;

            if (options.EnableRetry)
            {
                operationResult = await ExecuteWithRetryAsync(
                    operation, 
                    options, 
                    combinedCts.Token).ConfigureAwait(false);
            }
            else
            {
                operationResult = await operation(combinedCts.Token).ConfigureAwait(false);
            }

            result.Success = true;
            result.Result = operationResult;
            result.Duration = stopwatch.Elapsed;

            _logger.LogDebug("Async operation completed successfully in {Duration}ms", 
                result.Duration.TotalMilliseconds);

            return result;
        }
        catch (OperationCanceledException) when (cancellationToken.IsCancellationRequested)
        {
            _logger.LogInformation("Async operation was cancelled by user");
            result.Success = false;
            result.Duration = stopwatch.Elapsed;
            result.ErrorMessage = "Operation was cancelled";
            result.WasCancelled = true;
            return result;
        }
        catch (OperationCanceledException) when (!cancellationToken.IsCancellationRequested)
        {
            _logger.LogWarning("Async operation timed out after {TimeoutMs}ms", options.TimeoutMs);
            result.Success = false;
            result.Duration = stopwatch.Elapsed;
            result.ErrorMessage = $"Operation timed out after {options.TimeoutMs}ms";
            result.WasTimedOut = true;
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Async operation failed");
            result.Success = false;
            result.Duration = stopwatch.Elapsed;
            result.ErrorMessage = ex.Message;
            result.Exception = ex;
            return result;
        }
    }

    public async Task<IEnumerable<AsyncOptimizationResult>> OptimizeBatchAsyncOperationsAsync<T>(
        IEnumerable<Func<CancellationToken, Task<T>>> operations,
        AsyncOptimizationOptions options,
        CancellationToken cancellationToken = default)
    {
        var operationsList = operations.ToList();
        var results = new List<AsyncOptimizationResult>();

        _logger.LogInformation("Starting batch async optimization for {OperationCount} operations", 
            operationsList.Count);

        var tasks = operationsList.Select(async operation =>
        {
            return await OptimizeAsyncOperationAsync(operation, options, cancellationToken)
                .ConfigureAwait(false);
        });

        var batchResults = await Task.WhenAll(tasks).ConfigureAwait(false);
        results.AddRange(batchResults);

        var successCount = results.Count(r => r.Success);
        _logger.LogInformation("Batch async optimization completed: {SuccessCount}/{TotalCount} successful", 
            successCount, operationsList.Count);

        return results;
    }

    public async Task<AsyncStreamProcessingResult<TOutput>> ProcessAsyncStreamAsync<TInput, TOutput>(
        IAsyncEnumerable<TInput> inputStream,
        Func<TInput, CancellationToken, Task<TOutput>> processor,
        AsyncStreamProcessingOptions options,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var result = new AsyncStreamProcessingResult<TOutput>
        {
            StartTime = DateTime.UtcNow
        };

        _logger.LogInformation("Starting async stream processing with buffer size: {BufferSize}", 
            options.BufferSize);

        try
        {
            var outputList = new List<TOutput>();
            var buffer = new List<TInput>();
            var processedCount = 0;

            await foreach (var item in inputStream.WithCancellation(cancellationToken)
                .ConfigureAwait(false))
            {
                buffer.Add(item);

                // Process buffer when full or if we should flush immediately
                if (buffer.Count >= options.BufferSize || options.FlushImmediately)
                {
                    var batchResults = await ProcessBufferAsync(
                        buffer, processor, options, cancellationToken).ConfigureAwait(false);
                    
                    outputList.AddRange(batchResults);
                    processedCount += buffer.Count;
                    
                    // Report progress if callback provided
                    if (options.ProgressCallback != null)
                    {
                        await options.ProgressCallback(processedCount, outputList.Count)
                            .ConfigureAwait(false);
                    }

                    buffer.Clear();

                    // Memory management
                    if (options.EnableMemoryOptimization && 
                        processedCount % options.GcInterval == 0)
                    {
                        await OptimizeMemoryAsync().ConfigureAwait(false);
                    }
                }
            }

            // Process remaining items in buffer
            if (buffer.Count > 0)
            {
                var finalResults = await ProcessBufferAsync(
                    buffer, processor, options, cancellationToken).ConfigureAwait(false);
                outputList.AddRange(finalResults);
                processedCount += buffer.Count;
            }

            result.Success = true;
            result.Results = outputList;
            result.ProcessedCount = processedCount;
            result.Duration = stopwatch.Elapsed;

            _logger.LogInformation("Async stream processing completed: {ProcessedCount} items in {Duration}ms",
                processedCount, result.Duration.TotalMilliseconds);

            return result;
        }
        catch (OperationCanceledException) when (cancellationToken.IsCancellationRequested)
        {
            _logger.LogInformation("Async stream processing was cancelled");
            result.Success = false;
            result.Duration = stopwatch.Elapsed;
            result.ErrorMessage = "Processing was cancelled";
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Async stream processing failed");
            result.Success = false;
            result.Duration = stopwatch.Elapsed;
            result.ErrorMessage = ex.Message;
            result.Exception = ex;
            return result;
        }
    }

    public async Task<CancellationTokenOptimizationResult> OptimizeCancellationTokenPropagationAsync(
        Func<CancellationToken, Task> operation,
        TimeSpan timeout,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var result = new CancellationTokenOptimizationResult
        {
            StartTime = DateTime.UtcNow
        };

        try
        {
            // Create hierarchical cancellation tokens
            using var timeoutCts = new CancellationTokenSource(timeout);
            using var operationCts = new CancellationTokenSource();
            using var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(
                cancellationToken, timeoutCts.Token, operationCts.Token);

            // Track cancellation token state
            result.InitialTokenCanBeCanceled = cancellationToken.CanBeCanceled;
            result.InitialTokenIsCanceled = cancellationToken.IsCancellationRequested;

            // Execute operation with optimized cancellation
            await operation(combinedCts.Token).ConfigureAwait(false);

            result.Success = true;
            result.Duration = stopwatch.Elapsed;
            result.FinalTokenIsCanceled = combinedCts.Token.IsCancellationRequested;

            _logger.LogDebug("Cancellation token optimization completed successfully in {Duration}ms",
                result.Duration.TotalMilliseconds);

            return result;
        }
        catch (OperationCanceledException)
        {
            result.Success = false;
            result.Duration = stopwatch.Elapsed;
            result.WasCancelled = true;
            result.FinalTokenIsCanceled = true;
            result.CancellationReason = cancellationToken.IsCancellationRequested 
                ? "External cancellation" 
                : "Timeout or internal cancellation";

            _logger.LogInformation("Operation was cancelled: {Reason}", result.CancellationReason);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Cancellation token optimization failed");
            result.Success = false;
            result.Duration = stopwatch.Elapsed;
            result.ErrorMessage = ex.Message;
            result.Exception = ex;
            return result;
        }
    }

    public async Task<ThreadPoolOptimizationResult> OptimizeThreadPoolUsageAsync()
    {
        var result = new ThreadPoolOptimizationResult
        {
            StartTime = DateTime.UtcNow
        };

        try
        {
            // Capture initial thread pool state
            ThreadPool.GetMinThreads(out var initialMinWorker, out var initialMinCompletion);
            ThreadPool.GetMaxThreads(out var initialMaxWorker, out var initialMaxCompletion);
            ThreadPool.GetAvailableThreads(out var initialAvailableWorker, out var initialAvailableCompletion);

            result.InitialMinWorkerThreads = initialMinWorker;
            result.InitialMaxWorkerThreads = initialMaxWorker;
            result.InitialAvailableWorkerThreads = initialAvailableWorker;

            // Optimize thread pool settings for async operations
            var optimalMinWorker = Math.Max(Environment.ProcessorCount, 10);
            var optimalMaxWorker = Environment.ProcessorCount * 4;
            var optimalMinCompletion = Environment.ProcessorCount;
            var optimalMaxCompletion = Environment.ProcessorCount * 2;

            var minThreadsSet = ThreadPool.SetMinThreads(optimalMinWorker, optimalMinCompletion);
            var maxThreadsSet = ThreadPool.SetMaxThreads(optimalMaxWorker, optimalMaxCompletion);

            // Capture optimized thread pool state
            ThreadPool.GetMinThreads(out var optimizedMinWorker, out var optimizedMinCompletion);
            ThreadPool.GetMaxThreads(out var optimizedMaxWorker, out var optimizedMaxCompletion);
            ThreadPool.GetAvailableThreads(out var optimizedAvailableWorker, out var optimizedAvailableCompletion);

            result.OptimizedMinWorkerThreads = optimizedMinWorker;
            result.OptimizedMaxWorkerThreads = optimizedMaxWorker;
            result.OptimizedAvailableWorkerThreads = optimizedAvailableWorker;

            result.Success = minThreadsSet && maxThreadsSet;
            result.SettingsApplied = result.Success;

            _logger.LogInformation("Thread pool optimization completed. Min workers: {MinWorker} -> {OptMinWorker}, Max workers: {MaxWorker} -> {OptMaxWorker}",
                initialMinWorker, optimizedMinWorker, initialMaxWorker, optimizedMaxWorker);

            await Task.CompletedTask.ConfigureAwait(false);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Thread pool optimization failed");
            result.Success = false;
            result.ErrorMessage = ex.Message;
            result.Exception = ex;
            return result;
        }
    }

    private async Task<T> ExecuteWithRetryAsync<T>(
        Func<CancellationToken, Task<T>> operation,
        AsyncOptimizationOptions options,
        CancellationToken cancellationToken)
    {
        var attempt = 0;
        Exception lastException = null!;

        while (attempt < options.MaxRetryAttempts)
        {
            try
            {
                return await operation(cancellationToken).ConfigureAwait(false);
            }
            catch (Exception ex) when (attempt < options.MaxRetryAttempts - 1 && 
                                      !cancellationToken.IsCancellationRequested)
            {
                lastException = ex;
                attempt++;
                
                var delay = TimeSpan.FromMilliseconds(
                    options.RetryDelayMs * Math.Pow(options.RetryBackoffMultiplier, attempt - 1));

                _logger.LogWarning("Operation attempt {Attempt} failed, retrying in {DelayMs}ms: {Error}",
                    attempt, delay.TotalMilliseconds, ex.Message);

                await Task.Delay(delay, cancellationToken).ConfigureAwait(false);
            }
        }

        throw lastException;
    }

    private async Task<IEnumerable<TOutput>> ProcessBufferAsync<TInput, TOutput>(
        IEnumerable<TInput> buffer,
        Func<TInput, CancellationToken, Task<TOutput>> processor,
        AsyncStreamProcessingOptions options,
        CancellationToken cancellationToken)
    {
        var results = new List<TOutput>();
        var bufferList = buffer.ToList();

        if (options.ProcessInParallel)
        {
            var tasks = bufferList.Select(async item =>
            {
                return await processor(item, cancellationToken).ConfigureAwait(false);
            });

            var parallelResults = await Task.WhenAll(tasks).ConfigureAwait(false);
            results.AddRange(parallelResults);
        }
        else
        {
            foreach (var item in bufferList)
            {
                var result = await processor(item, cancellationToken).ConfigureAwait(false);
                results.Add(result);
            }
        }

        return results;
    }

    private async Task OptimizeMemoryAsync()
    {
        try
        {
            // Force garbage collection with configurable await
            await Task.Run(() =>
            {
                GC.Collect(2, GCCollectionMode.Optimized);
                GC.WaitForPendingFinalizers();
                GC.Collect(2, GCCollectionMode.Optimized);
            }).ConfigureAwait(false);

            // Small delay to allow GC to complete
            await Task.Delay(1).ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Memory optimization failed");
        }
    }
}
