using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Logging;

namespace XQ360.DataMigration.Web.Services.BusinessRules.Rules
{
    /// <summary>
    /// Business rule for validating driver card allocations
    /// </summary>
    public class DriverCardAllocationRule : BusinessRuleBase
    {
        private readonly ILogger<DriverCardAllocationRule> _logger;

        public DriverCardAllocationRule(ILogger<DriverCardAllocationRule> logger)
        {
            _logger = logger;
        }

        public override string Id => "DRIVER_CARD_ALLOCATION";
        public override string Name => "Driver Card Allocation Validation";
        public override string Description => "Validates that driver card allocations follow business rules including uniqueness, driver eligibility, and card availability";
        public override string Category => "Access Control";
        public override string[] ApplicableEntityTypes => new[] { "DriverCardAllocation", "Driver", "Card" };

        public override async Task<ValidationResult> ValidateAsync(object entity, ValidationContext context, CancellationToken cancellationToken = default)
        {
            var issues = new List<ValidationIssue>();

            try
            {
                // Extract driver and card IDs from the entity
                var driverId = GetDriverId(entity);
                var cardId = GetCardId(entity);

                if (driverId == Guid.Empty || cardId == Guid.Empty)
                {
                    issues.Add(CreateIssue(ValidationSeverity.Error,
                        "Invalid driver or card ID",
                        "DriverId/CardId",
                        $"Driver: {driverId}, Card: {cardId}",
                        "Ensure both driver and card IDs are valid GUIDs"));
                    return CreateFailureResult(issues.ToArray());
                }

                using var connection = new SqlConnection(context.ConnectionString);
                await connection.OpenAsync(cancellationToken);

                // Rule 1: Check if driver exists and is eligible for card allocation
                var driverEligibility = await CheckDriverEligibilityAsync(driverId, connection, cancellationToken);
                if (!driverEligibility.IsEligible)
                {
                    issues.Add(CreateIssue(ValidationSeverity.Error,
                        $"Driver is not eligible for card allocation: {driverEligibility.Reason}",
                        "DriverId",
                        driverId,
                        "Verify driver status and eligibility requirements"));
                }

                // Rule 2: Check if card exists and is available
                var cardAvailability = await CheckCardAvailabilityAsync(cardId, connection, cancellationToken);
                if (!cardAvailability.IsAvailable)
                {
                    issues.Add(CreateIssue(ValidationSeverity.Error,
                        $"Card is not available for allocation: {cardAvailability.Reason}",
                        "CardId",
                        cardId,
                        "Select a different card or check card status"));
                }

                // Rule 3: Check if driver already has a card assigned
                var existingCard = await CheckExistingDriverCardAsync(driverId, connection, cancellationToken);
                if (existingCard.HasCard)
                {
                    issues.Add(CreateIssue(ValidationSeverity.Warning,
                        $"Driver already has card {existingCard.ExistingCardId} assigned",
                        "DriverId",
                        driverId,
                        "Consider updating existing card assignment instead of creating new one"));
                }

                // Rule 4: Check if card is already assigned to another driver
                var existingAssignment = await CheckExistingCardAssignmentAsync(cardId, connection, cancellationToken);
                if (existingAssignment.HasAssignment)
                {
                    issues.Add(CreateIssue(ValidationSeverity.Error,
                        $"Card is already assigned to driver {existingAssignment.AssignedDriverId}",
                        "CardId",
                        cardId,
                        "Unassign card from current driver first or select a different card"));
                }

                // Rule 5: Validate Weigand number uniqueness
                var weigandCheck = await ValidateWeigandUniquenessAsync(cardId, connection, cancellationToken);
                if (!weigandCheck.IsUnique)
                {
                    issues.Add(CreateIssue(ValidationSeverity.Error,
                        $"Card Weigand number conflicts with existing card: {weigandCheck.ConflictingCardId}",
                        "CardId",
                        cardId,
                        "Ensure Weigand numbers are unique across all cards"));
                }

                // Rule 6: Check driver department access requirements
                var departmentAccess = await ValidateDepartmentAccessAsync(driverId, cardId, connection, cancellationToken);
                if (!departmentAccess.IsValid)
                {
                    issues.Add(CreateIssue(ValidationSeverity.Warning,
                        $"Card allocation may not provide required department access: {departmentAccess.Reason}",
                        "DriverId",
                        driverId,
                        "Verify card provides access to driver's department"));
                }

                // Rule 7: Validate card expiration date
                var expirationCheck = await ValidateCardExpirationAsync(cardId, connection, cancellationToken);
                if (!expirationCheck.IsValid)
                {
                    issues.Add(CreateIssue(ValidationSeverity.Warning,
                        $"Card expiration issue: {expirationCheck.Reason}",
                        "CardId",
                        cardId,
                        "Consider using a card with longer validity period"));
                }

                return issues.Any(i => i.Severity == ValidationSeverity.Error)
                    ? CreateFailureResult(issues.ToArray())
                    : new ValidationResult { IsValid = true, Issues = issues };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating driver card allocation for Driver: {DriverId}, Card: {CardId}",
                    GetDriverId(entity), GetCardId(entity));

                issues.Add(CreateIssue(ValidationSeverity.Error,
                    $"Validation error: {ex.Message}",
                    "System",
                    null,
                    "Contact system administrator"));

                return CreateFailureResult(issues.ToArray());
            }
        }

        private Guid GetDriverId(object entity)
        {
            var driverId = GetPropertyValue(entity, "DriverId");
            return driverId is Guid guid ? guid : Guid.Empty;
        }

        private Guid GetCardId(object entity)
        {
            var cardId = GetPropertyValue(entity, "CardId");
            return cardId is Guid guid ? guid : Guid.Empty;
        }

        private async Task<(bool IsEligible, string Reason)> CheckDriverEligibilityAsync(Guid driverId, SqlConnection connection, CancellationToken cancellationToken)
        {
            const string sql = @"
                SELECT d.IsActive, d.LicenseStatus, p.IsActive as PersonActive
                FROM [dbo].[Driver] d
                INNER JOIN [dbo].[Person] p ON d.Id = p.DriverId
                WHERE d.Id = @DriverId";

            using var command = new SqlCommand(sql, connection);
            command.Parameters.AddWithValue("@DriverId", driverId);

            using var reader = await command.ExecuteReaderAsync(cancellationToken);
            if (await reader.ReadAsync(cancellationToken))
            {
                var isDriverActive = reader.GetBoolean("IsActive");
                var licenseStatus = reader.IsDBNull("LicenseStatus") ? null : reader.GetString("LicenseStatus");
                var isPersonActive = reader.GetBoolean("PersonActive");

                if (!isDriverActive)
                    return (false, "Driver is inactive");

                if (!isPersonActive)
                    return (false, "Associated person record is inactive");

                if (!string.IsNullOrEmpty(licenseStatus) && licenseStatus.ToLower() != "valid")
                    return (false, $"Driver license status is {licenseStatus}");

                return (true, "Eligible");
            }

            return (false, "Driver not found");
        }

        private async Task<(bool IsAvailable, string Reason)> CheckCardAvailabilityAsync(Guid cardId, SqlConnection connection, CancellationToken cancellationToken)
        {
            const string sql = @"
                SELECT IsActive, Status, ExpiryDate
                FROM [dbo].[Card]
                WHERE Id = @CardId";

            using var command = new SqlCommand(sql, connection);
            command.Parameters.AddWithValue("@CardId", cardId);

            using var reader = await command.ExecuteReaderAsync(cancellationToken);
            if (await reader.ReadAsync(cancellationToken))
            {
                var isActive = reader.GetBoolean("IsActive");
                var status = reader.IsDBNull("Status") ? null : reader.GetString("Status");
                var expiryDate = reader.IsDBNull("ExpiryDate") ? (DateTime?)null : reader.GetDateTime("ExpiryDate");

                if (!isActive)
                    return (false, "Card is inactive");

                if (!string.IsNullOrEmpty(status) && status.ToLower() != "available")
                    return (false, $"Card status is {status}");

                if (expiryDate.HasValue && expiryDate.Value < DateTime.UtcNow)
                    return (false, "Card has expired");

                return (true, "Available");
            }

            return (false, "Card not found");
        }

        private async Task<(bool HasCard, Guid? ExistingCardId)> CheckExistingDriverCardAsync(Guid driverId, SqlConnection connection, CancellationToken cancellationToken)
        {
            const string sql = @"
                SELECT CardDetailsId
                FROM [dbo].[Driver]
                WHERE Id = @DriverId AND CardDetailsId IS NOT NULL";

            using var command = new SqlCommand(sql, connection);
            command.Parameters.AddWithValue("@DriverId", driverId);

            var result = await command.ExecuteScalarAsync(cancellationToken);
            if (result != null && result != DBNull.Value)
            {
                return (true, (Guid)result);
            }

            return (false, null);
        }

        private async Task<(bool HasAssignment, Guid? AssignedDriverId)> CheckExistingCardAssignmentAsync(Guid cardId, SqlConnection connection, CancellationToken cancellationToken)
        {
            const string sql = @"
                SELECT Id as DriverId
                FROM [dbo].[Driver]
                WHERE CardDetailsId = @CardId";

            using var command = new SqlCommand(sql, connection);
            command.Parameters.AddWithValue("@CardId", cardId);

            var result = await command.ExecuteScalarAsync(cancellationToken);
            if (result != null && result != DBNull.Value)
            {
                return (true, (Guid)result);
            }

            return (false, null);
        }

        private async Task<(bool IsUnique, Guid? ConflictingCardId)> ValidateWeigandUniquenessAsync(Guid cardId, SqlConnection connection, CancellationToken cancellationToken)
        {
            const string sql = @"
                SELECT c2.Id as ConflictingCardId
                FROM [dbo].[Card] c1
                INNER JOIN [dbo].[Card] c2 ON c1.WeigandNumber = c2.WeigandNumber
                WHERE c1.Id = @CardId AND c2.Id != @CardId";

            using var command = new SqlCommand(sql, connection);
            command.Parameters.AddWithValue("@CardId", cardId);

            var result = await command.ExecuteScalarAsync(cancellationToken);
            if (result != null && result != DBNull.Value)
            {
                return (false, (Guid)result);
            }

            return (true, null);
        }

        private async Task<(bool IsValid, string Reason)> ValidateDepartmentAccessAsync(Guid driverId, Guid cardId, SqlConnection connection, CancellationToken cancellationToken)
        {
            const string sql = @"
                SELECT p.DepartmentId
                FROM [dbo].[Driver] d
                INNER JOIN [dbo].[Person] p ON d.Id = p.DriverId
                WHERE d.Id = @DriverId";

            using var command = new SqlCommand(sql, connection);
            command.Parameters.AddWithValue("@DriverId", driverId);

            var result = await command.ExecuteScalarAsync(cancellationToken);
            if (result != null && result != DBNull.Value)
            {
                // In a real implementation, we would check if the card provides access to this department
                // For now, we'll assume it's valid but recommend verification
                return (true, "Department access validation recommended");
            }

            return (false, "Unable to determine driver's department");
        }

        private async Task<(bool IsValid, string Reason)> ValidateCardExpirationAsync(Guid cardId, SqlConnection connection, CancellationToken cancellationToken)
        {
            const string sql = @"
                SELECT ExpiryDate
                FROM [dbo].[Card]
                WHERE Id = @CardId";

            using var command = new SqlCommand(sql, connection);
            command.Parameters.AddWithValue("@CardId", cardId);

            var result = await command.ExecuteScalarAsync(cancellationToken);
            if (result != null && result != DBNull.Value)
            {
                var expiryDate = (DateTime)result;
                var daysUntilExpiry = (expiryDate - DateTime.UtcNow).Days;

                if (daysUntilExpiry < 0)
                    return (false, "Card has expired");

                if (daysUntilExpiry < 30)
                    return (false, $"Card expires in {daysUntilExpiry} days");

                return (true, "Card expiration is acceptable");
            }

            return (true, "No expiration date set");
        }
    }
}
