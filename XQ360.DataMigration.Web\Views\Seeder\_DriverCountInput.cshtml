@model XQ360.DataMigration.Web.Models.BulkSeederViewModel

<div class="driver-count-input">
    <div class="mb-3">
        <label for="driverCount" class="form-label">
            No. of drivers (<span id="driver-display-count">0</span>)
        </label>
        <input type="number" 
               id="driverCount" 
               name="DriverCount"
               class="form-control" 
               value="@Model.DriverCount"
               min="1"
               max="1000000"
               placeholder="Enter driver count"
               required>
        
        <div id="driver-validation-error" class="invalid-feedback" style="display: none;"></div>
        
        <div class="form-text d-flex justify-content-between align-items-center">
            <span>Range: <span id="driver-min-count">1</span> - <span id="driver-max-count">1,000,000</span> drivers</span>
            <span id="driver-environment-limit" class="text-warning" style="display: none;">
                <i class="fas fa-exclamation-triangle me-1"></i>
                Environment limit: <span id="driver-limit-value">0</span>
            </span>
        </div>
    </div>

    <!-- Real-time Validation Feedback -->
    <div id="driver-validation-feedback" style="display: none;">
        <div class="alert alert-light">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="d-flex align-items-center">
                        <div id="driver-validation-icon" class="me-2">
                            <i class="fas fa-check-circle text-success"></i>
                        </div>
                        <div>
                            <div class="fw-bold" id="driver-validation-title">Valid Range</div>
                            <div class="small text-muted" id="driver-validation-message">Count is within acceptable limits</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 text-end">
                    <div class="small text-muted">
                        <div>Estimated generation time</div>
                        <div class="fw-bold" id="driver-estimated-time">~1 minute</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Driver-to-Vehicle Ratio Warning -->
    <div id="driver-vehicle-ratio-warning" class="alert alert-info" style="display: none;">
        <div class="d-flex align-items-center">
            <i class="fas fa-info-circle me-2"></i>
            <div>
                <div class="fw-bold">Driver to Vehicle Ratio</div>
                <div class="small" id="ratio-message">
                    Current ratio: <span id="current-ratio">N/A</span>
                    <span id="ratio-recommendation" class="ms-2"></span>
                </div>
            </div>
        </div>
    </div>

    <!-- Large Volume Warning -->
    <div id="driver-large-volume-warning" class="alert alert-warning" style="display: none;">
        <div class="d-flex align-items-start">
            <i class="fas fa-exclamation-triangle me-2 mt-1"></i>
            <div class="flex-grow-1">
                <div class="fw-bold">Large Volume Operation</div>
                <div class="small mb-2">
                    You are about to generate a large number of drivers. This operation may take significant time and resources.
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="driver-large-volume-acknowledged">
                    <label class="form-check-label small" for="driver-large-volume-acknowledged">
                        I understand this is a large volume operation and may take considerable time
                    </label>
                </div>
            </div>
        </div>
    </div>

    <!-- Driver Data Requirements -->
    <div id="driver-requirements" class="driver-requirements mt-3" style="display: none;">
        <div class="alert alert-light">
            <div class="d-flex align-items-center mb-2">
                <i class="fas fa-id-card me-2"></i>
                <strong>Driver Data Requirements</strong>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <h6 class="small fw-bold mb-2">Required Fields:</h6>
                    <ul class="list-unstyled small">
                        <li><i class="fas fa-check text-success me-2"></i>Driver License Number</li>
                        <li><i class="fas fa-check text-success me-2"></i>Full Name</li>
                        <li><i class="fas fa-check text-success me-2"></i>Date of Birth</li>
                        <li><i class="fas fa-check text-success me-2"></i>License Expiry Date</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6 class="small fw-bold mb-2">Optional but Recommended:</h6>
                    <ul class="list-unstyled small">
                        <li><i class="fas fa-info text-info me-2"></i>Contact Information</li>
                        <li><i class="fas fa-info text-info me-2"></i>Emergency Contact</li>
                        <li><i class="fas fa-info text-info me-2"></i>Driver Classifications</li>
                        <li><i class="fas fa-info text-info me-2"></i>Training Records</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    const driverCountConfig = {
        minCount: 1,
        maxCount: 1000000,
        largeVolumeThreshold: 75000,
        environmentLimit: null,
        optimalRatioMin: 1.2, // 1.2 drivers per vehicle
        optimalRatioMax: 2.0   // 2.0 drivers per vehicle
    };

    $(document).ready(function() {
        // Initialize driver count validation
        initializeDriverCountValidation();
        
        // Handle input changes
        $('#driverCount').on('input', function() {
            validateDriverCount();
            updateDriverDisplayCount();
            updateEstimatedTime();
            updateDriverVehicleRatio();
        });

        $('#driverCount').on('blur', function() {
            validateDriverCount();
        });

        // Handle large volume acknowledgment
        $('#driver-large-volume-acknowledged').on('change', function() {
            validateDriverCount();
        });

        // Listen for vehicle count changes to update ratio
        $('#vehicleCount').on('input', function() {
            updateDriverVehicleRatio();
        });

        // Initialize with existing value
        if ($('#driverCount').val()) {
            validateDriverCount();
            updateDriverDisplayCount();
            updateEstimatedTime();
            updateDriverVehicleRatio();
        }
    });

    function initializeDriverCountValidation() {
        $('#driver-min-count').text(driverCountConfig.minCount.toLocaleString());
        $('#driver-max-count').text(driverCountConfig.maxCount.toLocaleString());
        
        // TODO: Load environment limits from server
        // loadEnvironmentLimits();
    }

    function validateDriverCount() {
        const input = $('#driverCount');
        const value = parseInt(input.val()) || 0;
        let isValid = true;
        let validationMessage = '';
        let validationTitle = 'Valid Range';
        let validationIcon = '<i class="fas fa-check-circle text-success"></i>';

        // Clear previous validation states
        input.removeClass('is-invalid');
        $('#driver-validation-error').hide();
        $('#driver-large-volume-warning').hide();

        if (value <= 0) {
            isValid = false;
            validationMessage = 'Driver count must be greater than 0';
            validationTitle = 'Invalid Count';
            validationIcon = '<i class="fas fa-exclamation-circle text-danger"></i>';
        } else if (value < driverCountConfig.minCount) {
            isValid = false;
            validationMessage = `Minimum driver count is ${driverCountConfig.minCount.toLocaleString()}`;
            validationTitle = 'Below Minimum';
            validationIcon = '<i class="fas fa-exclamation-triangle text-warning"></i>';
        } else if (value > driverCountConfig.maxCount) {
            isValid = false;
            validationMessage = `Maximum driver count is ${driverCountConfig.maxCount.toLocaleString()}`;
            validationTitle = 'Above Maximum';
            validationIcon = '<i class="fas fa-exclamation-circle text-danger"></i>';
        } else if (driverCountConfig.environmentLimit && value > driverCountConfig.environmentLimit) {
            isValid = false;
            validationMessage = `Environment limit is ${driverCountConfig.environmentLimit.toLocaleString()} drivers`;
            validationTitle = 'Environment Limit Exceeded';
            validationIcon = '<i class="fas fa-exclamation-triangle text-warning"></i>';
        } else if (value >= driverCountConfig.largeVolumeThreshold) {
            // Large volume - check acknowledgment
            $('#driver-large-volume-warning').show();
            
            if (!$('#driver-large-volume-acknowledged').is(':checked')) {
                isValid = false;
                validationMessage = 'Please acknowledge the large volume operation';
                validationTitle = 'Acknowledgment Required';
                validationIcon = '<i class="fas fa-exclamation-triangle text-warning"></i>';
            } else {
                validationMessage = 'Large volume operation acknowledged';
                validationTitle = 'Large Volume';
                validationIcon = '<i class="fas fa-check-circle text-success"></i>';
            }
        }

        // Show/hide validation feedback
        if (value > 0) {
            $('#driver-validation-feedback').show();
            $('#driver-requirements').show();
        } else {
            $('#driver-validation-feedback').hide();
            $('#driver-requirements').hide();
        }

        // Update validation display
        $('#driver-validation-icon').html(validationIcon);
        $('#driver-validation-title').text(validationTitle);
        $('#driver-validation-message').text(validationMessage);

        // Show error state if invalid
        if (!isValid && value > 0) {
            input.addClass('is-invalid');
            $('#driver-validation-error').text(validationMessage).show();
        }

        // Update field validation state
        updateFieldValidation('driverCount', isValid);

        return isValid;
    }

    function updateDriverDisplayCount() {
        const value = parseInt($('#driverCount').val()) || 0;
        $('#driver-display-count').text(value.toLocaleString());
    }

    function updateEstimatedTime() {
        const driverCount = parseInt($('#driverCount').val()) || 0;
        
        if (driverCount <= 0) {
            $('#driver-estimated-time').text('N/A');
            return;
        }

        // Rough estimation: 200 drivers per minute
        const driverMinutes = Math.ceil(driverCount / 200);
        const totalMinutes = driverMinutes + 1; // Add setup time

        let timeText;
        if (totalMinutes < 60) {
            timeText = `~${totalMinutes} minute${totalMinutes !== 1 ? 's' : ''}`;
        } else {
            const hours = Math.floor(totalMinutes / 60);
            const remainingMinutes = totalMinutes % 60;
            timeText = `~${hours}h ${remainingMinutes}m`;
        }

        $('#driver-estimated-time').text(timeText);
    }

    function updateDriverVehicleRatio() {
        const driverCount = parseInt($('#driverCount').val()) || 0;
        const vehicleCount = parseInt($('#vehicleCount').val()) || 0;

        if (driverCount <= 0 || vehicleCount <= 0) {
            $('#driver-vehicle-ratio-warning').hide();
            return;
        }

        const ratio = driverCount / vehicleCount;
        $('#current-ratio').text(`${ratio.toFixed(1)}:1 (${driverCount.toLocaleString()} drivers : ${vehicleCount.toLocaleString()} vehicles)`);

        let recommendationText = '';
        let alertClass = 'alert-info';

        if (ratio < driverCountConfig.optimalRatioMin) {
            recommendationText = 'Consider adding more drivers - typically 1.2-2.0 drivers per vehicle is optimal';
            alertClass = 'alert-warning';
        } else if (ratio > driverCountConfig.optimalRatioMax) {
            recommendationText = 'You have many drivers per vehicle - this is acceptable but may indicate over-staffing';
            alertClass = 'alert-info';
        } else {
            recommendationText = 'Good driver-to-vehicle ratio';
            alertClass = 'alert-success';
        }

        $('#ratio-recommendation').text(recommendationText);
        $('#driver-vehicle-ratio-warning')
            .removeClass('alert-info alert-warning alert-success')
            .addClass(alertClass)
            .show();
    }

    function loadEnvironmentLimits() {
        // TODO: Implement when environment service is available
        $.ajax({
            url: '/api/environment/limits',
            method: 'GET',
            success: function(limits) {
                if (limits.driverLimit) {
                    driverCountConfig.environmentLimit = limits.driverLimit;
                    $('#driver-limit-value').text(limits.driverLimit.toLocaleString());
                    $('#driver-environment-limit').show();
                }
            },
            error: function() {
                // Ignore errors for now
            }
        });
    }

    // Export for use by other components
    window.getDriverCount = function() {
        return parseInt($('#driverCount').val()) || 0;
    };

    window.isDriverCountValid = function() {
        return validateDriverCount();
    };
</script>
