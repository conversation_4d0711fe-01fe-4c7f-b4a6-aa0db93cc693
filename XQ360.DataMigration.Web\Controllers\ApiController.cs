using Microsoft.AspNetCore.Mvc;
using XQ360.DataMigration.Web.Models;

namespace XQ360.DataMigration.Web.Controllers;

/// <summary>
/// General API controller for supporting the seeder UI
/// Provides endpoints for dealers, customers, and validation
/// </summary>
[ApiController]
[Route("api")]
[Produces("application/json")]
public class ApiController : ControllerBase
{
    private readonly ILogger<ApiController> _logger;

    public ApiController(ILogger<ApiController> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Search for dealers by name or subdomain
    /// </summary>
    /// <param name="query">Search query</param>
    /// <returns>List of matching dealers</returns>
    [HttpGet("dealers/search")]
    public ActionResult<List<DealerInfo>> SearchDealers([FromQuery] string query)
    {
        try
        {
            // For now, return mock data
            // In a real implementation, this would query the database
            var mockDealers = GetMockDealers()
                .Where(d => string.IsNullOrEmpty(query) || 
                           d.Name.Contains(query, StringComparison.OrdinalIgnoreCase) ||
                           d.Subdomain.Contains(query, StringComparison.OrdinalIgnoreCase))
                .Take(10)
                .ToList();

            return Ok(mockDealers);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching dealers with query: {Query}", query);
            return Problem(
                title: "Search Error",
                detail: "An error occurred while searching for dealers",
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    /// <summary>
    /// Get customers by dealer ID
    /// </summary>
    /// <param name="dealerId">Dealer identifier</param>
    /// <returns>List of customers for the dealer</returns>
    [HttpGet("customers/by-dealer/{dealerId}")]
    public ActionResult<List<CustomerInfo>> GetCustomersByDealer(string dealerId)
    {
        try
        {
            // For now, return mock data
            // In a real implementation, this would query the database
            var mockCustomers = GetMockCustomers()
                .Where(c => c.DealerId == dealerId)
                .ToList();

            return Ok(mockCustomers);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting customers for dealer: {DealerId}", dealerId);
            return Problem(
                title: "Data Retrieval Error",
                detail: "An error occurred while retrieving customers",
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    /// <summary>
    /// Get a specific customer by ID
    /// </summary>
    /// <param name="customerId">Customer identifier</param>
    /// <returns>Customer information</returns>
    [HttpGet("customers/{customerId}")]
    public ActionResult<CustomerInfo> GetCustomer(string customerId)
    {
        try
        {
            // For now, return mock data
            // In a real implementation, this would query the database
            var customer = GetMockCustomers()
                .FirstOrDefault(c => c.Id == customerId);

            if (customer == null)
            {
                return NotFound(new ProblemDetails
                {
                    Title = "Customer Not Found",
                    Detail = $"Customer with ID {customerId} was not found",
                    Status = StatusCodes.Status404NotFound
                });
            }

            return Ok(customer);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting customer: {CustomerId}", customerId);
            return Problem(
                title: "Data Retrieval Error",
                detail: "An error occurred while retrieving customer information",
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    /// <summary>
    /// Create a new customer
    /// </summary>
    /// <param name="request">Customer creation request</param>
    /// <returns>Created customer information</returns>
    [HttpPost("customers")]
    public ActionResult<CustomerInfo> CreateCustomer([FromBody] CreateCustomerRequest request)
    {
        try
        {
            if (request == null)
            {
                return BadRequest(new ProblemDetails
                {
                    Title = "Invalid Request",
                    Detail = "Request body cannot be null",
                    Status = StatusCodes.Status400BadRequest
                });
            }

            if (!ModelState.IsValid)
            {
                return BadRequest(new ValidationProblemDetails(ModelState));
            }

            // For now, create a mock customer
            // In a real implementation, this would save to the database
            var newCustomer = new CustomerInfo
            {
                Id = Guid.NewGuid().ToString(),
                Name = request.Name,
                ContactName = request.ContactName,
                ContactEmail = request.ContactEmail,
                ContactPhone = request.ContactPhone,
                DealerId = request.DealerId,
                IsActive = true
            };

            _logger.LogInformation("Created new customer: {CustomerName} for dealer: {DealerId}", 
                newCustomer.Name, newCustomer.DealerId);

            return CreatedAtAction(nameof(GetCustomer), new { customerId = newCustomer.Id }, newCustomer);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating customer");
            return Problem(
                title: "Creation Error",
                detail: "An error occurred while creating the customer",
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    /// <summary>
    /// Validate seeder configuration before starting
    /// </summary>
    /// <param name="request">Validation request</param>
    /// <returns>Validation result</returns>
    [HttpPost("bulk-seeder/validate")]
    public ActionResult<ValidationResult> ValidateSeederConfiguration([FromBody] ValidateSeederRequest request)
    {
        try
        {
            if (request == null)
            {
                return BadRequest(new ProblemDetails
                {
                    Title = "Invalid Request",
                    Detail = "Request body cannot be null",
                    Status = StatusCodes.Status400BadRequest
                });
            }

            var errors = new List<string>();

            // Validate dealer
            if (string.IsNullOrEmpty(request.DealerId))
            {
                errors.Add("Dealer selection is required");
            }

            // Validate customer
            if (string.IsNullOrEmpty(request.CustomerId))
            {
                errors.Add("Customer selection is required");
            }

            // Validate counts
            if (request.VehicleCount <= 0)
            {
                errors.Add("Vehicle count must be greater than 0");
            }

            if (request.DriverCount <= 0)
            {
                errors.Add("Driver count must be greater than 0");
            }

            // Check for reasonable limits
            if (request.VehicleCount > 100000)
            {
                errors.Add("Vehicle count exceeds maximum limit of 100,000");
            }

            if (request.DriverCount > 200000)
            {
                errors.Add("Driver count exceeds maximum limit of 200,000");
            }

            var result = new ValidationResult
            {
                Success = errors.Count == 0,
                ValidationErrors = errors,
                Summary = errors.Count == 0 ? "Configuration is valid" : $"Found {errors.Count} validation error(s)"
            };

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating seeder configuration");
            return Problem(
                title: "Validation Error",
                detail: "An error occurred while validating the configuration",
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    /// <summary>
    /// Start a seeding operation
    /// </summary>
    /// <param name="request">Seeding start request</param>
    /// <returns>Session information</returns>
    [HttpPost("bulk-seeder/start")]
    public ActionResult<object> StartSeeding([FromBody] CreateSeederSessionRequest request)
    {
        try
        {
            if (request == null)
            {
                return BadRequest(new ProblemDetails
                {
                    Title = "Invalid Request",
                    Detail = "Request body cannot be null",
                    Status = StatusCodes.Status400BadRequest
                });
            }

            // For now, return a mock session ID
            // In a real implementation, this would start the actual seeding process
            var sessionId = Guid.NewGuid();

            _logger.LogInformation("Started seeding session: {SessionId}", sessionId);

            return Ok(new 
            { 
                sessionId = sessionId,
                status = "Started",
                message = "Seeding operation started successfully"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting seeding operation");
            return Problem(
                title: "Start Error",
                detail: "An error occurred while starting the seeding operation",
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    /// <summary>
    /// Cancel a seeding operation
    /// </summary>
    /// <param name="sessionId">Session identifier</param>
    /// <returns>Cancellation result</returns>
    [HttpPost("bulk-seeder/cancel/{sessionId}")]
    public ActionResult<object> CancelSeeding(Guid sessionId)
    {
        try
        {
            _logger.LogInformation("Cancelled seeding session: {SessionId}", sessionId);

            return Ok(new 
            { 
                sessionId = sessionId,
                status = "Cancelled",
                message = "Seeding operation cancelled successfully"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cancelling seeding operation: {SessionId}", sessionId);
            return Problem(
                title: "Cancellation Error",
                detail: "An error occurred while cancelling the seeding operation",
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    #region Mock Data

    private static List<DealerInfo> GetMockDealers()
    {
        return new List<DealerInfo>
        {
            new() { Id = "1", Name = "ABC Transport", Subdomain = "abc-transport", IsActive = true },
            new() { Id = "2", Name = "XYZ Logistics", Subdomain = "xyz-logistics", IsActive = true },
            new() { Id = "3", Name = "Metro Fleet Services", Subdomain = "metro-fleet", IsActive = true },
            new() { Id = "4", Name = "Global Transport Co", Subdomain = "global-transport", IsActive = true },
            new() { Id = "5", Name = "City Express", Subdomain = "city-express", IsActive = true },
            new() { Id = "6", Name = "Prime Logistics", Subdomain = "prime-logistics", IsActive = false },
            new() { Id = "7", Name = "Elite Transport", Subdomain = "elite-transport", IsActive = true },
            new() { Id = "8", Name = "Swift Delivery", Subdomain = "swift-delivery", IsActive = true },
            new() { Id = "9", Name = "Northern Fleet", Subdomain = "northern-fleet", IsActive = true },
            new() { Id = "10", Name = "Southern Transport", Subdomain = "southern-transport", IsActive = true }
        };
    }

    private static List<CustomerInfo> GetMockCustomers()
    {
        return new List<CustomerInfo>
        {
            // Customers for dealer 1 (ABC Transport)
            new() { Id = "c1", Name = "Acme Corporation", DealerId = "1", ContactName = "John Smith", ContactEmail = "<EMAIL>", IsActive = true },
            new() { Id = "c2", Name = "Beta Industries", DealerId = "1", ContactName = "Jane Doe", ContactEmail = "<EMAIL>", IsActive = true },
            new() { Id = "c3", Name = "Gamma Solutions", DealerId = "1", ContactName = "Bob Wilson", ContactEmail = "<EMAIL>", IsActive = true },

            // Customers for dealer 2 (XYZ Logistics)
            new() { Id = "c4", Name = "Delta Manufacturing", DealerId = "2", ContactName = "Alice Johnson", ContactEmail = "<EMAIL>", IsActive = true },
            new() { Id = "c5", Name = "Epsilon Services", DealerId = "2", ContactName = "Charlie Brown", ContactEmail = "<EMAIL>", IsActive = true },

            // Customers for dealer 3 (Metro Fleet Services)
            new() { Id = "c6", Name = "Zeta Construction", DealerId = "3", ContactName = "Diana Prince", ContactEmail = "<EMAIL>", IsActive = true },
            new() { Id = "c7", Name = "Theta Retail", DealerId = "3", ContactName = "Evan Davis", ContactEmail = "<EMAIL>", IsActive = true },
            new() { Id = "c8", Name = "Iota Hospitality", DealerId = "3", ContactName = "Fiona Green", ContactEmail = "<EMAIL>", IsActive = true },

            // More customers for other dealers...
            new() { Id = "c9", Name = "Kappa Technology", DealerId = "4", ContactName = "George Miller", ContactEmail = "<EMAIL>", IsActive = true },
            new() { Id = "c10", Name = "Lambda Healthcare", DealerId = "5", ContactName = "Helen Clark", ContactEmail = "<EMAIL>", IsActive = true }
        };
    }

    #endregion
}

/// <summary>
/// Request model for validating seeder configuration
/// </summary>
public class ValidateSeederRequest
{
    public string? DealerId { get; set; }
    public string? CustomerId { get; set; }
    public int VehicleCount { get; set; }
    public int DriverCount { get; set; }
}
