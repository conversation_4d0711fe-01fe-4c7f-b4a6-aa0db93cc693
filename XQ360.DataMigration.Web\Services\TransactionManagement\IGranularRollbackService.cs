namespace XQ360.DataMigration.Web.Services.TransactionManagement
{
    /// <summary>
    /// Service for granular rollback capabilities with entity-level control
    /// </summary>
    public interface IGranularRollbackService
    {
        /// <summary>
        /// Creates a checkpoint for incremental rollback
        /// </summary>
        Task<ICheckpoint> CreateCheckpointAsync(string name, Guid sessionId, CheckpointOptions options, CancellationToken cancellationToken = default);

        /// <summary>
        /// Performs entity-level rollback (e.g., rollback vehicles but keep drivers)
        /// </summary>
        Task<EntityRollbackResult> RollbackEntitiesAsync(EntityRollbackRequest request, CancellationToken cancellationToken = default);

        /// <summary>
        /// Performs partial rollback to a specific checkpoint
        /// </summary>
        Task<PartialRollbackResult> RollbackToCheckpointAsync(Guid checkpointId, PartialRollbackOptions options, CancellationToken cancellationToken = default);

        /// <summary>
        /// Performs selective rollback of specific operations
        /// </summary>
        Task<SelectiveRollbackResult> RollbackOperationsAsync(IEnumerable<string> operationIds, SelectiveRollbackOptions options, CancellationToken cancellationToken = default);

        /// <summary>
        /// Initiates automatic recovery procedures for failed operations
        /// </summary>
        Task<RecoveryResult> InitiateRecoveryAsync(RecoveryRequest request, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets available checkpoints for a session
        /// </summary>
        Task<IEnumerable<ICheckpoint>> GetCheckpointsAsync(Guid sessionId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Validates rollback feasibility before execution
        /// </summary>
        Task<RollbackValidationResult> ValidateRollbackAsync(RollbackValidationRequest request, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets rollback impact analysis
        /// </summary>
        Task<RollbackImpactAnalysis> AnalyzeRollbackImpactAsync(RollbackImpactRequest request, CancellationToken cancellationToken = default);
    }

    /// <summary>
    /// Checkpoint interface for incremental rollback
    /// </summary>
    public interface ICheckpoint
    {
        /// <summary>
        /// Unique checkpoint identifier
        /// </summary>
        Guid CheckpointId { get; }

        /// <summary>
        /// Checkpoint name
        /// </summary>
        string Name { get; }

        /// <summary>
        /// Session this checkpoint belongs to
        /// </summary>
        Guid SessionId { get; }

        /// <summary>
        /// Timestamp when checkpoint was created
        /// </summary>
        DateTime CreatedAt { get; }

        /// <summary>
        /// Database state snapshot
        /// </summary>
        IDatabaseSnapshot DatabaseSnapshot { get; }

        /// <summary>
        /// API state snapshot
        /// </summary>
        IApiSnapshot ApiSnapshot { get; }

        /// <summary>
        /// Operations executed before this checkpoint
        /// </summary>
        IReadOnlyList<string> CompletedOperations { get; }

        /// <summary>
        /// Checkpoint metadata
        /// </summary>
        Dictionary<string, object> Metadata { get; }
    }

    /// <summary>
    /// Database state snapshot interface
    /// </summary>
    public interface IDatabaseSnapshot
    {
        /// <summary>
        /// Snapshot identifier
        /// </summary>
        Guid SnapshotId { get; }

        /// <summary>
        /// Entity counts at snapshot time
        /// </summary>
        Dictionary<string, int> EntityCounts { get; }

        /// <summary>
        /// Table checksums for integrity verification
        /// </summary>
        Dictionary<string, string> TableChecksums { get; }

        /// <summary>
        /// Snapshot creation timestamp
        /// </summary>
        DateTime CreatedAt { get; }
    }

    /// <summary>
    /// API state snapshot interface
    /// </summary>
    public interface IApiSnapshot
    {
        /// <summary>
        /// Snapshot identifier
        /// </summary>
        Guid SnapshotId { get; }

        /// <summary>
        /// API entity states
        /// </summary>
        Dictionary<string, object> EntityStates { get; }

        /// <summary>
        /// Pending API operations
        /// </summary>
        List<string> PendingOperations { get; }

        /// <summary>
        /// Snapshot creation timestamp
        /// </summary>
        DateTime CreatedAt { get; }
    }

    /// <summary>
    /// Checkpoint creation options
    /// </summary>
    public class CheckpointOptions
    {
        public bool IncludeDatabaseSnapshot { get; set; } = true;
        public bool IncludeApiSnapshot { get; set; } = true;
        public string[] IncludedTables { get; set; } = Array.Empty<string>();
        public string[] ExcludedTables { get; set; } = Array.Empty<string>();
        public bool CompressSnapshots { get; set; } = true;
        public TimeSpan RetentionPeriod { get; set; } = TimeSpan.FromDays(7);
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// Entity rollback request
    /// </summary>
    public class EntityRollbackRequest
    {
        public Guid SessionId { get; set; }
        public string[] EntityTypes { get; set; } = Array.Empty<string>();
        public EntityRollbackStrategy Strategy { get; set; } = EntityRollbackStrategy.DeleteInserted;
        public bool PreserveRelatedEntities { get; set; } = true;
        public bool ValidateIntegrity { get; set; } = true;
        public Dictionary<string, object> Options { get; set; } = new();
    }

    /// <summary>
    /// Entity rollback strategies
    /// </summary>
    public enum EntityRollbackStrategy
    {
        DeleteInserted,     // Delete entities that were inserted
        RestoreOriginal,    // Restore entities to original state
        MarkAsRolledBack,   // Mark entities as rolled back but keep them
        Custom              // Use custom rollback logic
    }

    /// <summary>
    /// Partial rollback options
    /// </summary>
    public class PartialRollbackOptions
    {
        public bool RollbackDatabase { get; set; } = true;
        public bool RollbackApi { get; set; } = true;
        public bool ValidateConsistency { get; set; } = true;
        public bool CreateBackupBeforeRollback { get; set; } = true;
        public string[] ExcludedOperations { get; set; } = Array.Empty<string>();
        public Dictionary<string, object> CustomOptions { get; set; } = new();
    }

    /// <summary>
    /// Selective rollback options
    /// </summary>
    public class SelectiveRollbackOptions
    {
        public bool RollbackDependentOperations { get; set; } = true;
        public bool ValidateIntegrity { get; set; } = true;
        public bool CreateCompensatingActions { get; set; } = true;
        public int MaxDependencyDepth { get; set; } = 5;
        public Dictionary<string, object> OperationOptions { get; set; } = new();
    }

    /// <summary>
    /// Recovery request
    /// </summary>
    public class RecoveryRequest
    {
        public Guid SessionId { get; set; }
        public RecoveryType Type { get; set; }
        public string[] FailedOperations { get; set; } = Array.Empty<string>();
        public RecoveryStrategy Strategy { get; set; } = RecoveryStrategy.Automatic;
        public bool AttemptDataRepair { get; set; } = true;
        public int MaxRetryAttempts { get; set; } = 3;
        public TimeSpan RetryDelay { get; set; } = TimeSpan.FromSeconds(5);
        public Dictionary<string, object> RecoveryOptions { get; set; } = new();
    }

    /// <summary>
    /// Recovery types
    /// </summary>
    public enum RecoveryType
    {
        OperationFailure,
        DataCorruption,
        ConsistencyViolation,
        SystemFailure,
        UserRequested
    }

    /// <summary>
    /// Recovery strategies
    /// </summary>
    public enum RecoveryStrategy
    {
        Automatic,          // Automatic recovery using predefined procedures
        Manual,             // Manual recovery with user intervention
        Rollback,           // Rollback to last known good state
        Repair,             // Attempt to repair corrupted data
        Recreate            // Recreate from source data
    }

    /// <summary>
    /// Entity rollback result
    /// </summary>
    public class EntityRollbackResult
    {
        public bool Success { get; set; }
        public Guid SessionId { get; set; }
        public string[] RolledBackEntityTypes { get; set; } = Array.Empty<string>();
        public Dictionary<string, int> RolledBackCounts { get; set; } = new();
        public List<string> Errors { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
        public TimeSpan Duration { get; set; }
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// Partial rollback result
    /// </summary>
    public class PartialRollbackResult
    {
        public bool Success { get; set; }
        public Guid CheckpointId { get; set; }
        public Guid SessionId { get; set; }
        public List<string> RolledBackOperations { get; set; } = new();
        public DatabaseRollbackResult DatabaseResult { get; set; } = new();
        public ApiRollbackResult ApiResult { get; set; } = new();
        public List<string> Errors { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
        public TimeSpan Duration { get; set; }
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// Selective rollback result
    /// </summary>
    public class SelectiveRollbackResult
    {
        public bool Success { get; set; }
        public List<string> RolledBackOperations { get; set; } = new();
        public List<string> DependentOperations { get; set; } = new();
        public List<CompensatingActionResult> CompensatingActions { get; set; } = new();
        public List<string> Errors { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
        public TimeSpan Duration { get; set; }
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// Recovery result
    /// </summary>
    public class RecoveryResult
    {
        public bool Success { get; set; }
        public Guid SessionId { get; set; }
        public RecoveryType Type { get; set; }
        public RecoveryStrategy StrategyUsed { get; set; }
        public List<RecoveryAction> ActionsPerformed { get; set; } = new();
        public List<string> RecoveredOperations { get; set; } = new();
        public List<string> UnrecoverableOperations { get; set; } = new();
        public List<string> Errors { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
        public TimeSpan Duration { get; set; }
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// Recovery action
    /// </summary>
    public class RecoveryAction
    {
        public string ActionId { get; set; } = string.Empty;
        public RecoveryActionType Type { get; set; }
        public string Description { get; set; } = string.Empty;
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; }
        public TimeSpan Duration { get; set; }
        public Dictionary<string, object> Details { get; set; } = new();
    }

    /// <summary>
    /// Recovery action types
    /// </summary>
    public enum RecoveryActionType
    {
        DataRepair,
        OperationRetry,
        StateReset,
        ConsistencyFix,
        BackupRestore,
        ManualIntervention
    }

    /// <summary>
    /// Database rollback result
    /// </summary>
    public class DatabaseRollbackResult
    {
        public bool Success { get; set; }
        public Dictionary<string, int> RestoredCounts { get; set; } = new();
        public List<string> RestoredTables { get; set; } = new();
        public List<string> Errors { get; set; } = new();
        public TimeSpan Duration { get; set; }
    }

    /// <summary>
    /// API rollback result
    /// </summary>
    public class ApiRollbackResult
    {
        public bool Success { get; set; }
        public List<string> RolledBackEntities { get; set; } = new();
        public List<CompensatingActionResult> CompensatingActions { get; set; } = new();
        public List<string> Errors { get; set; } = new();
        public TimeSpan Duration { get; set; }
    }

    /// <summary>
    /// Rollback validation request
    /// </summary>
    public class RollbackValidationRequest
    {
        public Guid SessionId { get; set; }
        public RollbackType Type { get; set; }
        public Guid? CheckpointId { get; set; }
        public string[] OperationIds { get; set; } = Array.Empty<string>();
        public string[] EntityTypes { get; set; } = Array.Empty<string>();
        public bool ValidateDependencies { get; set; } = true;
        public bool ValidateIntegrity { get; set; } = true;
    }

    /// <summary>
    /// Rollback types
    /// </summary>
    public enum RollbackType
    {
        Entity,
        Partial,
        Selective,
        Complete
    }

    /// <summary>
    /// Rollback validation result
    /// </summary>
    public class RollbackValidationResult
    {
        public bool IsValid { get; set; }
        public List<ValidationIssue> Issues { get; set; } = new();
        public List<ValidationWarning> Warnings { get; set; } = new();
        public RollbackFeasibility Feasibility { get; set; }
        public EstimatedImpact EstimatedImpact { get; set; } = new();
        public List<string> Prerequisites { get; set; } = new();
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// Rollback feasibility levels
    /// </summary>
    public enum RollbackFeasibility
    {
        FullyFeasible,
        PartiallyFeasible,
        NotFeasible,
        RequiresManualIntervention
    }

    /// <summary>
    /// Estimated rollback impact
    /// </summary>
    public class EstimatedImpact
    {
        public Dictionary<string, int> AffectedEntityCounts { get; set; } = new();
        public List<string> AffectedSystems { get; set; } = new();
        public TimeSpan EstimatedDuration { get; set; }
        public RiskLevel RiskLevel { get; set; }
        public List<string> PotentialSideEffects { get; set; } = new();
    }

    /// <summary>
    /// Risk levels for rollback operations
    /// </summary>
    public enum RiskLevel
    {
        Low,
        Medium,
        High,
        Critical
    }

    /// <summary>
    /// Rollback impact analysis request
    /// </summary>
    public class RollbackImpactRequest
    {
        public Guid SessionId { get; set; }
        public RollbackType Type { get; set; }
        public Guid? CheckpointId { get; set; }
        public string[] OperationIds { get; set; } = Array.Empty<string>();
        public string[] EntityTypes { get; set; } = Array.Empty<string>();
        public bool IncludeDependencyAnalysis { get; set; } = true;
        public bool IncludeRiskAssessment { get; set; } = true;
    }

    /// <summary>
    /// Rollback impact analysis result
    /// </summary>
    public class RollbackImpactAnalysis
    {
        public Guid SessionId { get; set; }
        public RollbackType Type { get; set; }
        public EstimatedImpact Impact { get; set; } = new();
        public DependencyAnalysis Dependencies { get; set; } = new();
        public RiskAssessment Risk { get; set; } = new();
        public List<string> Recommendations { get; set; } = new();
        public DateTime AnalyzedAt { get; set; }
        public TimeSpan AnalysisDuration { get; set; }
    }

    /// <summary>
    /// Dependency analysis
    /// </summary>
    public class DependencyAnalysis
    {
        public Dictionary<string, List<string>> EntityDependencies { get; set; } = new();
        public Dictionary<string, List<string>> OperationDependencies { get; set; } = new();
        public List<string> CircularDependencies { get; set; } = new();
        public int MaxDependencyDepth { get; set; }
    }

    /// <summary>
    /// Risk assessment
    /// </summary>
    public class RiskAssessment
    {
        public RiskLevel OverallRisk { get; set; }
        public Dictionary<string, RiskLevel> ComponentRisks { get; set; } = new();
        public List<RiskFactor> RiskFactors { get; set; } = new();
        public List<string> MitigationStrategies { get; set; } = new();
    }

    /// <summary>
    /// Risk factor
    /// </summary>
    public class RiskFactor
    {
        public string FactorId { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public RiskLevel Severity { get; set; }
        public double Probability { get; set; }
        public string Impact { get; set; } = string.Empty;
        public string Mitigation { get; set; } = string.Empty;
    }
}
