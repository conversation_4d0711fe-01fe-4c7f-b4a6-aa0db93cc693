/**
 * Seeder Wizard JavaScript Module
 * Replaces Vue.js functionality for the bulk seeder interface
 */

const SeederWizard = (function() {
    'use strict';

    // Configuration and state
    let config = {
        sessionId: null,
        isImporting: false,
        currentEnvironment: null,
        hubConnection: null
    };

    // Validation state tracking
    let validationStates = {
        environment: true, // Always true since it uses existing system
        dealer: false,
        customer: false,
        vehicleCount: false,
        driverCount: false
    };

    // Pre-import validation result
    let preImportValidation = null;

    /**
     * Initialize the seeder wizard
     */
    function init(options = {}) {
        config = { ...config, ...options };
        
        console.log('Initializing Seeder Wizard...', config);
        
        // Initialize SignalR connection for progress tracking
        initializeSignalR();
        
        // Initialize form handlers
        initializeFormHandlers();
        
        // Initialize summary updates
        updateConfigurationSummary();
        
        // Show progress tracker if import is active
        if (config.isImporting && config.sessionId) {
            showProgressTracker();
        }
    }

    /**
     * Initialize SignalR connection for real-time progress updates
     */
    function initializeSignalR() {
        if (typeof signalR === 'undefined') {
            console.warn('SignalR not available - progress updates will be disabled');
            return;
        }

        try {
            // Use existing MigrationHub from main application
            config.hubConnection = new signalR.HubConnectionBuilder()
                .withUrl("/migrationHub")
                .build();

            config.hubConnection.start().then(function () {
                console.log('SignalR connection established');
                
                // Subscribe to progress updates
                config.hubConnection.on("ProgressUpdate", function (sessionId, progress, message) {
                    if (sessionId === config.sessionId) {
                        updateProgress(progress, message);
                    }
                });
                
                config.hubConnection.on("StatusUpdate", function (sessionId, status, message) {
                    if (sessionId === config.sessionId) {
                        handleStatusUpdate(status, message);
                    }
                });
                
            }).catch(function (err) {
                console.error('SignalR connection failed:', err);
            });
            
        } catch (error) {
            console.error('Failed to initialize SignalR:', error);
        }
    }

    /**
     * Initialize form event handlers
     */
    function initializeFormHandlers() {
        // Handle form submission
        $('#seederForm').on('submit', function(e) {
            e.preventDefault();
            
            if (validateAllFields()) {
                startSeedingOperation();
            }
        });

        // Handle field changes for real-time validation
        $(document).on('fieldValidationChanged', function(e, fieldName, isValid) {
            updateFieldValidation(fieldName, isValid);
        });

        // Handle validate configuration button
        window.validateConfiguration = function() {
            if (validateAllFields()) {
                performPreImportValidation();
            } else {
                showErrorMessage('Please complete all required fields before validation.');
            }
        };

        // Handle cancel seeding operation
        window.cancelSeeding = function() {
            if (config.sessionId && confirm('Are you sure you want to cancel the seeding operation?')) {
                cancelSeedingOperation();
            }
        };
    }

    /**
     * Update field validation state
     */
    function updateFieldValidation(fieldName, isValid) {
        validationStates[fieldName] = isValid;
        updateConfigurationSummary();
        updateStartButton();
        
        // Trigger custom event for other components
        $(document).trigger('fieldValidationChanged', [fieldName, isValid]);
    }

    /**
     * Validate all form fields
     */
    function validateAllFields() {
        let allValid = true;
        
        // Check each validation state
        for (const [field, isValid] of Object.entries(validationStates)) {
            if (!isValid) {
                allValid = false;
                console.log(`Validation failed for field: ${field}`);
            }
        }
        
        return allValid;
    }

    /**
     * Update the configuration summary sidebar
     */
    function updateConfigurationSummary() {
        const allFieldsValid = validateAllFields();
        
        if (allFieldsValid) {
            $('#completeSummary').show();
            $('#incompleteSummary').hide();
            
            // Update summary values
            updateSummaryValues();
        } else {
            $('#completeSummary').hide();
            $('#incompleteSummary').show();
        }
    }

    /**
     * Update summary values in the sidebar
     */
    function updateSummaryValues() {
        // Update dealer
        const dealerName = $('#selected-dealer-name').val();
        $('#summaryDealer').text(dealerName || 'Not selected');
        
        // Update customer
        const customerName = $('#selected-customer-name').val();
        $('#summaryCustomer').text(customerName || 'Not selected');
        
        // Update counts
        const vehicleCount = parseInt($('#vehicleCount').val()) || 0;
        const driverCount = parseInt($('#driverCount').val()) || 0;
        
        $('#summaryVehicles').text(`${vehicleCount.toLocaleString()} vehicles`);
        $('#summaryDrivers').text(`${driverCount.toLocaleString()} drivers`);
        
        // Update estimated time
        updateEstimatedTime(vehicleCount, driverCount);
    }

    /**
     * Update estimated time for the operation
     */
    function updateEstimatedTime(vehicleCount, driverCount) {
        if (!vehicleCount || !driverCount) {
            $('#summaryTime').text('Calculating...');
            return;
        }
        
        // Rough estimation: 100 vehicles per minute, 200 drivers per minute
        const vehicleMinutes = Math.ceil(vehicleCount / 100);
        const driverMinutes = Math.ceil(driverCount / 200);
        const totalMinutes = Math.max(vehicleMinutes, driverMinutes) + 2; // Add setup time
        
        let timeText;
        if (totalMinutes < 60) {
            timeText = `~${totalMinutes} minute${totalMinutes !== 1 ? 's' : ''}`;
        } else {
            const hours = Math.floor(totalMinutes / 60);
            const remainingMinutes = totalMinutes % 60;
            timeText = `~${hours}h ${remainingMinutes}m`;
        }
        
        $('#summaryTime').text(timeText);
    }

    /**
     * Update the start button state
     */
    function updateStartButton() {
        const allValid = validateAllFields();
        const hasValidation = preImportValidation && preImportValidation.success;
        const canStart = allValid && hasValidation && !config.isImporting;
        
        $('#startSeedingBtn').prop('disabled', !canStart);
    }

    /**
     * Perform pre-import validation
     */
    function performPreImportValidation() {
        if (!validateAllFields()) {
            return;
        }

        const validationData = {
            dealerId: $('#selected-dealer-id').val(),
            customerId: $('#selected-customer-id').val(),
            vehicleCount: parseInt($('#vehicleCount').val()),
            driverCount: parseInt($('#driverCount').val())
        };

        showLoadingMessage('Validating configuration...');

        $.ajax({
            url: '/api/bulk-seeder/validate',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(validationData),
            success: function(result) {
                hideLoadingMessage();
                preImportValidation = result;
                displayValidationResults(result);
                updateStartButton();
            },
            error: function(xhr, status, error) {
                hideLoadingMessage();
                const message = xhr.responseJSON?.message || error;
                showErrorMessage('Validation failed: ' + message);
                preImportValidation = { success: false, errors: [message] };
                updateStartButton();
            }
        });
    }

    /**
     * Display validation results
     */
    function displayValidationResults(result) {
        const resultsDiv = $('#validationResults');
        const statusDiv = $('#validationStatus');
        
        if (result.success) {
            statusDiv.html(`
                <div class="text-success">
                    <i class="fas fa-check-circle me-1"></i>
                    Validation successful - ready to start seeding
                </div>
            `);
        } else {
            const errors = result.errors || ['Unknown validation error'];
            statusDiv.html(`
                <div class="text-danger">
                    <i class="fas fa-exclamation-circle me-1"></i>
                    Validation failed:
                    <ul class="mb-0 mt-1">
                        ${errors.map(error => `<li>${error}</li>`).join('')}
                    </ul>
                </div>
            `);
        }
        
        resultsDiv.show();
    }

    /**
     * Start the seeding operation
     */
    function startSeedingOperation() {
        if (!validateAllFields() || !preImportValidation?.success) {
            showErrorMessage('Please validate the configuration before starting the seeding operation.');
            return;
        }

        const seedingData = {
            dealerId: $('#selected-dealer-id').val(),
            customerId: $('#selected-customer-id').val(),
            vehicleCount: parseInt($('#vehicleCount').val()),
            driverCount: parseInt($('#driverCount').val()),
            generateData: true,
            dryRun: false
        };

        config.isImporting = true;
        showLoadingMessage('Starting seeding operation...');

        $.ajax({
            url: '/api/bulk-seeder/start',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(seedingData),
            success: function(result) {
                hideLoadingMessage();
                
                if (result.sessionId) {
                    config.sessionId = result.sessionId;
                    showProgressTracker();
                    showSuccessMessage('Seeding operation started successfully!');
                } else {
                    config.isImporting = false;
                    showErrorMessage('Failed to start seeding operation: No session ID returned');
                }
            },
            error: function(xhr, status, error) {
                hideLoadingMessage();
                config.isImporting = false;
                const message = xhr.responseJSON?.message || error;
                showErrorMessage('Failed to start seeding operation: ' + message);
            }
        });
    }

    /**
     * Cancel the seeding operation
     */
    function cancelSeedingOperation() {
        if (!config.sessionId) return;

        $.ajax({
            url: `/api/bulk-seeder/cancel/${config.sessionId}`,
            method: 'POST',
            success: function() {
                showSuccessMessage('Seeding operation cancelled');
            },
            error: function(xhr, status, error) {
                const message = xhr.responseJSON?.message || error;
                showErrorMessage('Failed to cancel operation: ' + message);
            }
        });
    }

    /**
     * Show progress tracker
     */
    function showProgressTracker() {
        $('#progressTracker').show();
        updateProgress(0, 'Initializing...');
    }

    /**
     * Hide progress tracker
     */
    function hideProgressTracker() {
        $('#progressTracker').hide();
        config.isImporting = false;
        config.sessionId = null;
    }

    /**
     * Update progress display
     */
    function updateProgress(percentage, message) {
        const progressBar = $('#progressBar');
        const statusDiv = $('#progressStatus');
        
        progressBar.css('width', percentage + '%')
                  .attr('aria-valuenow', percentage)
                  .text(Math.round(percentage) + '%');
        
        statusDiv.text(message || 'Processing...');
    }

    /**
     * Handle status updates from SignalR
     */
    function handleStatusUpdate(status, message) {
        console.log('Status update:', status, message);
        
        if (status === 'completed') {
            updateProgress(100, 'Operation completed successfully');
            hideProgressTracker();
            showSuccessMessage('Seeding operation completed successfully!');
            
            // Navigate to sessions view after delay
            setTimeout(() => {
                if (confirm('Seeding completed! Would you like to view the session details?')) {
                    window.location.href = '/Home/Index'; // Or sessions view when available
                }
            }, 2000);
            
        } else if (status === 'failed') {
            hideProgressTracker();
            showErrorMessage('Seeding operation failed: ' + (message || 'Unknown error'));
            
        } else if (status === 'cancelled') {
            hideProgressTracker();
            showWarningMessage('Seeding operation was cancelled');
        }
    }

    /**
     * Utility functions for messages
     */
    function showSuccessMessage(message) {
        showMessage(message, 'success');
    }

    function showErrorMessage(message) {
        showMessage(message, 'danger');
    }

    function showWarningMessage(message) {
        showMessage(message, 'warning');
    }

    function showLoadingMessage(message) {
        showMessage(`<i class="fas fa-spinner fa-spin me-2"></i>${message}`, 'info');
    }

    function hideLoadingMessage() {
        // Remove loading messages
        $('.alert').filter(function() {
            return $(this).html().includes('fa-spinner');
        }).remove();
    }

    function showMessage(message, type) {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show mt-3" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;
        
        // Remove existing messages of same type
        $(`.alert-${type}`).remove();
        
        // Add new message at top of form
        $('#seederForm').before(alertHtml);
        
        // Auto-dismiss after 5 seconds (except error messages)
        if (type !== 'danger') {
            setTimeout(() => {
                $(`.alert-${type}`).alert('close');
            }, 5000);
        }
    }

    // Export public interface
    return {
        init: init,
        updateFieldValidation: updateFieldValidation,
        validateConfiguration: validateConfiguration,
        cancelSeeding: cancelSeeding
    };

})();

// Make functions available globally for inline event handlers
window.updateFieldValidation = SeederWizard.updateFieldValidation;
window.validateConfiguration = function() {
    if (typeof SeederWizard.validateConfiguration === 'function') {
        SeederWizard.validateConfiguration();
    }
};
window.cancelSeeding = function() {
    if (typeof SeederWizard.cancelSeeding === 'function') {
        SeederWizard.cancelSeeding();
    }
};

// Auto-initialize when DOM is ready
$(document).ready(function() {
    // SeederWizard will be initialized from the view with proper config
});
