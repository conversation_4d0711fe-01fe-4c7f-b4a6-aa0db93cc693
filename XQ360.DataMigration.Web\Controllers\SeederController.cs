using Microsoft.AspNetCore.Mvc;
using XQ360.DataMigration.Web.Models;
using XQ360.DataMigration.Services;

namespace XQ360.DataMigration.Web.Controllers;

/// <summary>
/// MVC Controller for the Data Seeder user interface
/// </summary>
public class SeederController : Controller
{
    private readonly ILogger<SeederController> _logger;
    private readonly IEnvironmentConfigurationService _environmentService;

    public SeederController(
        ILogger<SeederController> logger,
        IEnvironmentConfigurationService environmentService)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _environmentService = environmentService ?? throw new ArgumentNullException(nameof(environmentService));
    }

    /// <summary>
    /// Display the main seeder wizard interface
    /// </summary>
    /// <returns>Seeder wizard view</returns>
    public IActionResult Index()
    {
        var model = new BulkSeederViewModel
        {
            CurrentEnvironment = _environmentService.CurrentEnvironmentKey,
            Dealers = new List<DealerInfo>(),
            Customers = new List<CustomerInfo>(),
            ValidationState = new ValidationState
            {
                IsEnvironmentValid = true // Always valid since using existing system
            }
        };

        return View(model);
    }

    /// <summary>
    /// Handle form submission to create a new seeding session
    /// This redirects to the API endpoint
    /// </summary>
    /// <param name="model">Seeder view model</param>
    /// <returns>Redirect or view with validation errors</returns>
    [HttpPost]
    public async Task<IActionResult> CreateSession(BulkSeederViewModel model)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                // Reload data for the view
                model.CurrentEnvironment = _environmentService.CurrentEnvironmentKey;
                model.Dealers = new List<DealerInfo>();
                model.Customers = new List<CustomerInfo>();
                
                return View("Index", model);
            }

            // The actual session creation will be handled by JavaScript calling the API
            // This is here as a fallback for non-JavaScript scenarios
            
            TempData["SuccessMessage"] = "Seeding session created successfully!";
            return RedirectToAction("Index");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating seeding session");
            ModelState.AddModelError("", "An error occurred while creating the seeding session. Please try again.");
            
            // Reload data for the view
            model.CurrentEnvironment = _environmentService.CurrentEnvironmentKey;
            model.Dealers = new List<DealerInfo>();
            model.Customers = new List<CustomerInfo>();
            
            return View("Index", model);
        }
    }

    /// <summary>
    /// Display sessions management page (placeholder for future implementation)
    /// </summary>
    /// <returns>Sessions view</returns>
    public IActionResult Sessions()
    {
        // This would show a list of seeding sessions
        // For now, redirect to the main seeder page
        return RedirectToAction("Index");
    }
}
