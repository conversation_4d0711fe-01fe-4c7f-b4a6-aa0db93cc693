using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Collections.Concurrent;
using System.Diagnostics;
using XQ360.DataMigration.Web.Models;
using XQ360.DataMigration.Services;

namespace XQ360.DataMigration.Web.Services.BulkSeeder;

/// <summary>
/// Connection Pool Optimization Service implementation for Phase 1.2.3
/// Provides dedicated connection pools for staging vs production operations
/// Performance optimized with Min 10, Max 100 connections, 30-second idle timeout
/// </summary>
public class ConnectionPoolService : IConnectionPoolService
{
    private readonly ILogger<ConnectionPoolService> _logger;
    private readonly BulkSeederConfiguration _options;
    private readonly IEnvironmentConfigurationService _environmentService;
    
    // Connection pools by type
    private readonly ConcurrentQueue<PooledConnection> _stagingPool = new();
    private readonly ConcurrentQueue<PooledConnection> _productionPool = new();
    private readonly ConcurrentQueue<PooledConnection> _readOnlyPool = new();
    
    // Pool statistics
    private readonly ConnectionPoolStats _stagingStats = new() { PoolType = ConnectionPoolType.Staging, PoolName = "Staging" };
    private readonly ConnectionPoolStats _productionStats = new() { PoolType = ConnectionPoolType.Production, PoolName = "Production" };
    private readonly ConnectionPoolStats _readOnlyStats = new() { PoolType = ConnectionPoolType.ReadOnly, PoolName = "ReadOnly" };
    
    // Synchronization and cleanup
    private readonly Timer _cleanupTimer;
    private readonly SemaphoreSlim _poolLock = new(1, 1);
    private bool _disposed = false;

    public ConnectionPoolService(
        ILogger<ConnectionPoolService> logger,
        IOptions<BulkSeederConfiguration> options,
        IEnvironmentConfigurationService environmentService)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
        _environmentService = environmentService ?? throw new ArgumentNullException(nameof(environmentService));

        // Initialize pool configurations
        InitializePoolConfigurations();
        
        // Start cleanup timer
        _cleanupTimer = new Timer(CleanupExpiredConnections, null, 
            TimeSpan.FromSeconds(30), TimeSpan.FromSeconds(30));
        
        _logger.LogInformation("Connection pool service initialized with staging pool ({MinStaging}-{MaxStaging}) and production pool ({MinProduction}-{MaxProduction})",
            _options.StagingPoolMinConnections, _options.StagingPoolMaxConnections,
            _options.ProductionPoolMinConnections, _options.ProductionPoolMaxConnections);
    }

    public async Task<SqlConnection> GetStagingConnectionAsync(CancellationToken cancellationToken = default)
    {
        return await GetConnectionAsync(ConnectionPoolType.Staging, cancellationToken);
    }

    public async Task<SqlConnection> GetProductionConnectionAsync(CancellationToken cancellationToken = default)
    {
        return await GetConnectionAsync(ConnectionPoolType.Production, cancellationToken);
    }

    public async Task<SqlConnection> GetConnectionAsync(ConnectionPoolType poolType, CancellationToken cancellationToken = default)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(ConnectionPoolService));

        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            var pool = GetPoolByType(poolType);
            var stats = GetStatsByType(poolType);

            // Try to get an existing connection from the pool
            if (pool.TryDequeue(out var pooledConnection))
            {
                if (pooledConnection.IsValid && !pooledConnection.IsExpired)
                {
                    pooledConnection.LastUsed = DateTime.UtcNow;
                    stats.TotalConnectionsReused++;
                    stats.AverageAcquisitionTime = stopwatch.Elapsed;
                    
                    _logger.LogDebug("Reused {PoolType} connection from pool", poolType);
                    return pooledConnection.Connection;
                }
                else
                {
                    // Connection is expired or invalid, dispose it
                    await DisposeConnectionSafelyAsync(pooledConnection.Connection);
                    stats.TotalConnectionsDestroyed++;
                }
            }

            // Create new connection if pool is below max capacity
            if (stats.TotalConnections < GetMaxConnections(poolType))
            {
                var connection = await CreateOptimizedConnectionAsync(poolType, cancellationToken);
                stats.TotalConnectionsCreated++;
                stats.ActiveConnections++;
                stats.TotalConnections++;
                stats.TotalRequestsServed++;
                
                stopwatch.Stop();
                stats.AverageAcquisitionTime = stopwatch.Elapsed;
                if (stopwatch.Elapsed > stats.MaxAcquisitionTime)
                {
                    stats.MaxAcquisitionTime = stopwatch.Elapsed;
                }

                _logger.LogDebug("Created new {PoolType} connection ({ActiveConnections}/{MaxConnections})", 
                    poolType, stats.ActiveConnections, GetMaxConnections(poolType));
                
                return connection;
            }

            // Pool is at capacity, wait and retry (simplified approach)
            await Task.Delay(100, cancellationToken);
            stats.PendingRequests++;
            
            _logger.LogWarning("{PoolType} pool at capacity, retrying...", poolType);
            return await GetConnectionAsync(poolType, cancellationToken);
        }
        catch (Exception ex)
        {
            var stats = GetStatsByType(poolType);
            stats.ConnectionFailures++;
            
            _logger.LogError(ex, "Failed to get {PoolType} connection", poolType);
            throw;
        }
    }

    public async Task<SqlConnection> CreateOptimizedConnectionAsync(
        ConnectionPoolType poolType,
        CancellationToken cancellationToken = default)
    {
        var baseConnectionString = _environmentService.CurrentMigrationConfiguration.DatabaseConnection;
        var optimizedConnectionString = OptimizeConnectionString(baseConnectionString, poolType);

        var connection = new SqlConnection(optimizedConnectionString);
        await connection.OpenAsync(cancellationToken);

        // Apply pool-specific optimizations
        await ApplyConnectionOptimizationsAsync(connection, poolType);

        return connection;
    }

    public async Task ReturnConnectionAsync(SqlConnection connection, ConnectionPoolType poolType)
    {
        if (_disposed || connection == null) return;

        try
        {
            var pool = GetPoolByType(poolType);
            var stats = GetStatsByType(poolType);

            // Validate connection is still usable
            if (connection.State == System.Data.ConnectionState.Open)
            {
                var pooledConnection = new PooledConnection
                {
                    Connection = connection,
                    CreatedAt = DateTime.UtcNow,
                    LastUsed = DateTime.UtcNow,
                    PoolType = poolType
                };

                pool.Enqueue(pooledConnection);
                stats.ActiveConnections--;
                stats.IdleConnections++;
                
                _logger.LogDebug("Returned {PoolType} connection to pool", poolType);
            }
            else
            {
                await DisposeConnectionSafelyAsync(connection);
                stats.ActiveConnections--;
                stats.TotalConnections--;
                stats.TotalConnectionsDestroyed++;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to return {PoolType} connection to pool", poolType);
            await DisposeConnectionSafelyAsync(connection);
        }
    }

    public async Task<ConnectionPoolMetrics> GetPoolMetricsAsync()
    {
        return new ConnectionPoolMetrics
        {
            StagingPool = _stagingStats,
            ProductionPool = _productionStats,
            ReadOnlyPool = _readOnlyStats,
            TotalActiveConnections = _stagingStats.ActiveConnections + _productionStats.ActiveConnections + _readOnlyStats.ActiveConnections,
            TotalIdleConnections = _stagingStats.IdleConnections + _productionStats.IdleConnections + _readOnlyStats.IdleConnections,
            TotalConnectionsCreated = (int)(_stagingStats.TotalConnectionsCreated + _productionStats.TotalConnectionsCreated + _readOnlyStats.TotalConnectionsCreated),
            TotalConnectionsDestroyed = (int)(_stagingStats.TotalConnectionsDestroyed + _productionStats.TotalConnectionsDestroyed + _readOnlyStats.TotalConnectionsDestroyed),
            PoolUtilizationPercentage = CalculateOverallUtilization()
        };
    }

    public async Task OptimizePoolsAsync()
    {
        await _poolLock.WaitAsync();
        try
        {
            _logger.LogInformation("Optimizing connection pools...");

            // Remove expired connections
            await CleanupExpiredConnectionsInternal();

            // Pre-populate pools to minimum if needed
            await EnsureMinimumConnectionsAsync();

            _logger.LogInformation("Connection pool optimization completed");
        }
        finally
        {
            _poolLock.Release();
        }
    }

    public async Task ValidatePoolHealthAsync()
    {
        var healthResults = new List<PoolHealthResult>();

        // Validate each pool
        healthResults.Add(await ValidatePoolHealthInternal(ConnectionPoolType.Staging));
        healthResults.Add(await ValidatePoolHealthInternal(ConnectionPoolType.Production));
        healthResults.Add(await ValidatePoolHealthInternal(ConnectionPoolType.ReadOnly));

        // Log health summary
        var overallHealthy = healthResults.All(r => r.IsHealthy);
        _logger.LogInformation("Pool health validation completed: {OverallHealth}", 
            overallHealthy ? "Healthy" : "Issues Detected");

        if (!overallHealthy)
        {
            var issues = healthResults.SelectMany(r => r.HealthIssues).ToList();
            _logger.LogWarning("Pool health issues detected: {Issues}", string.Join("; ", issues));
        }
    }

    public async Task ClearSessionConnectionsAsync(Guid sessionId)
    {
        // For this simplified implementation, we don't track session-specific connections
        // In a full implementation, this would clear connections associated with a specific session
        _logger.LogInformation("Session {SessionId} connection cleanup requested", sessionId);
    }

    // Helper methods

    private void InitializePoolConfigurations()
    {
        _stagingStats.MinConnections = _options.StagingPoolMinConnections;
        _stagingStats.MaxConnections = _options.StagingPoolMaxConnections;
        _stagingStats.IdleTimeoutSeconds = _options.ConnectionIdleTimeoutSeconds;
        _stagingStats.LifetimeSeconds = _options.ConnectionLifetimeSeconds;

        _productionStats.MinConnections = _options.ProductionPoolMinConnections;
        _productionStats.MaxConnections = _options.ProductionPoolMaxConnections;
        _productionStats.IdleTimeoutSeconds = _options.ConnectionIdleTimeoutSeconds;
        _productionStats.LifetimeSeconds = _options.ConnectionLifetimeSeconds;

        _readOnlyStats.MinConnections = 5;
        _readOnlyStats.MaxConnections = 50;
        _readOnlyStats.IdleTimeoutSeconds = _options.ConnectionIdleTimeoutSeconds;
        _readOnlyStats.LifetimeSeconds = _options.ConnectionLifetimeSeconds;
    }

    private ConcurrentQueue<PooledConnection> GetPoolByType(ConnectionPoolType poolType)
    {
        return poolType switch
        {
            ConnectionPoolType.Staging => _stagingPool,
            ConnectionPoolType.Production => _productionPool,
            ConnectionPoolType.ReadOnly => _readOnlyPool,
            _ => throw new ArgumentException($"Unknown pool type: {poolType}")
        };
    }

    private ConnectionPoolStats GetStatsByType(ConnectionPoolType poolType)
    {
        return poolType switch
        {
            ConnectionPoolType.Staging => _stagingStats,
            ConnectionPoolType.Production => _productionStats,
            ConnectionPoolType.ReadOnly => _readOnlyStats,
            _ => throw new ArgumentException($"Unknown pool type: {poolType}")
        };
    }

    private int GetMaxConnections(ConnectionPoolType poolType)
    {
        return poolType switch
        {
            ConnectionPoolType.Staging => _options.StagingPoolMaxConnections,
            ConnectionPoolType.Production => _options.ProductionPoolMaxConnections,
            ConnectionPoolType.ReadOnly => 50,
            _ => 10
        };
    }

    private string OptimizeConnectionString(string baseConnectionString, ConnectionPoolType poolType)
    {
        var builder = new SqlConnectionStringBuilder(baseConnectionString);

        // Apply pool-specific optimizations
        switch (poolType)
        {
            case ConnectionPoolType.Staging:
                builder.Pooling = true;
                builder.MinPoolSize = _options.StagingPoolMinConnections;
                builder.MaxPoolSize = _options.StagingPoolMaxConnections;
                builder.CommandTimeout = _options.BulkCopyTimeout;
                break;

            case ConnectionPoolType.Production:
                builder.Pooling = true;
                builder.MinPoolSize = _options.ProductionPoolMinConnections;
                builder.MaxPoolSize = _options.ProductionPoolMaxConnections;
                builder.CommandTimeout = _options.CommandTimeout;
                break;

            case ConnectionPoolType.ReadOnly:
                builder.Pooling = true;
                builder.MinPoolSize = 5;
                builder.MaxPoolSize = 50;
                builder.ApplicationIntent = ApplicationIntent.ReadOnly;
                break;
        }

        builder.LoadBalanceTimeout = _options.ConnectionIdleTimeoutSeconds;
        builder.ConnectRetryCount = 3;
        builder.ConnectRetryInterval = 10;

        return builder.ConnectionString;
    }

    private async Task ApplyConnectionOptimizationsAsync(SqlConnection connection, ConnectionPoolType poolType)
    {
        try
        {
            // Apply pool-specific SQL optimizations
            var optimizations = poolType switch
            {
                ConnectionPoolType.Staging => new[]
                {
                    "SET ARITHABORT ON",
                    "SET ANSI_NULLS ON", 
                    "SET ANSI_PADDING ON",
                    "SET ANSI_WARNINGS ON",
                    "SET NUMERIC_ROUNDABORT OFF",
                    "SET QUOTED_IDENTIFIER ON"
                },
                ConnectionPoolType.Production => new[]
                {
                    "SET ARITHABORT ON",
                    "SET ANSI_NULLS ON",
                    "SET ANSI_PADDING ON",
                    "SET ANSI_WARNINGS ON",
                    "SET NUMERIC_ROUNDABORT OFF",
                    "SET QUOTED_IDENTIFIER ON",
                    "SET TRANSACTION ISOLATION LEVEL READ COMMITTED"
                },
                _ => Array.Empty<string>()
            };

            foreach (var optimization in optimizations)
            {
                using var command = new SqlCommand(optimization, connection);
                await command.ExecuteNonQueryAsync();
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to apply connection optimizations for {PoolType}", poolType);
        }
    }

    private decimal CalculateOverallUtilization()
    {
        var totalCapacity = _stagingStats.MaxConnections + _productionStats.MaxConnections + _readOnlyStats.MaxConnections;
        var totalUsed = _stagingStats.TotalConnections + _productionStats.TotalConnections + _readOnlyStats.TotalConnections;
        
        return totalCapacity > 0 ? (decimal)totalUsed / totalCapacity * 100 : 0;
    }

    private async Task EnsureMinimumConnectionsAsync()
    {
        // Simplified implementation - in production, this would pre-create minimum connections
        await Task.CompletedTask;
    }

    private async Task<PoolHealthResult> ValidatePoolHealthInternal(ConnectionPoolType poolType)
    {
        var result = new PoolHealthResult { IsHealthy = true };
        var stats = GetStatsByType(poolType);

        // Check utilization
        if (stats.UtilizationPercentage > 90)
        {
            result.IsHealthy = false;
            result.HealthIssues.Add($"{poolType} pool utilization is high ({stats.UtilizationPercentage:F1}%)");
        }

        // Check for connection failures
        if (stats.ConnectionFailures > 10)
        {
            result.IsHealthy = false;
            result.HealthIssues.Add($"{poolType} pool has {stats.ConnectionFailures} connection failures");
        }

        return result;
    }

    private void CleanupExpiredConnections(object? state)
    {
        _ = Task.Run(async () =>
        {
            try
            {
                await CleanupExpiredConnectionsInternal();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during connection pool cleanup");
            }
        });
    }

    private async Task CleanupExpiredConnectionsInternal()
    {
        await CleanupPoolConnections(_stagingPool, _stagingStats, ConnectionPoolType.Staging);
        await CleanupPoolConnections(_productionPool, _productionStats, ConnectionPoolType.Production);
        await CleanupPoolConnections(_readOnlyPool, _readOnlyStats, ConnectionPoolType.ReadOnly);
    }

    private async Task CleanupPoolConnections(ConcurrentQueue<PooledConnection> pool, ConnectionPoolStats stats, ConnectionPoolType poolType)
    {
        var expiredConnections = new List<PooledConnection>();
        var validConnections = new List<PooledConnection>();

        // Drain the pool to check connections
        while (pool.TryDequeue(out var connection))
        {
            if (connection.IsExpired || !connection.IsValid)
            {
                expiredConnections.Add(connection);
            }
            else
            {
                validConnections.Add(connection);
            }
        }

        // Re-add valid connections
        foreach (var connection in validConnections)
        {
            pool.Enqueue(connection);
        }

        // Dispose expired connections
        foreach (var connection in expiredConnections)
        {
            await DisposeConnectionSafelyAsync(connection.Connection);
            stats.TotalConnectionsDestroyed++;
            stats.IdleConnections--;
            stats.TotalConnections--;
        }

        if (expiredConnections.Count > 0)
        {
            _logger.LogDebug("Cleaned up {ExpiredCount} expired connections from {PoolType} pool", 
                expiredConnections.Count, poolType);
        }
    }

    private async Task DisposeConnectionSafelyAsync(SqlConnection connection)
    {
        try
        {
            if (connection?.State == System.Data.ConnectionState.Open)
            {
                await connection.CloseAsync();
            }
            connection?.Dispose();
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error disposing SQL connection");
        }
    }

    public void Dispose()
    {
        if (_disposed) return;

        _disposed = true;
        _cleanupTimer?.Dispose();

        // Dispose all connections in pools
        DisposePool(_stagingPool);
        DisposePool(_productionPool);
        DisposePool(_readOnlyPool);

        _poolLock?.Dispose();
        
        _logger.LogInformation("Connection pool service disposed");
    }

    private void DisposePool(ConcurrentQueue<PooledConnection> pool)
    {
        while (pool.TryDequeue(out var connection))
        {
            _ = DisposeConnectionSafelyAsync(connection.Connection);
        }
    }
}

/// <summary>
/// Represents a connection in the connection pool with metadata
/// </summary>
internal class PooledConnection
{
    public SqlConnection Connection { get; set; } = null!;
    public DateTime CreatedAt { get; set; }
    public DateTime LastUsed { get; set; }
    public ConnectionPoolType PoolType { get; set; }
    
    public bool IsExpired => DateTime.UtcNow - LastUsed > TimeSpan.FromSeconds(30);
    public bool IsValid => Connection?.State == System.Data.ConnectionState.Open;
}
