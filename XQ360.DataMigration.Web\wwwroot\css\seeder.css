/**
 * Seeder Component Styles
 * CSS for the bulk data seeder interface
 */

/* Main Seeder Form Card */
.seeder-form-card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 2rem;
}

.seeder-form-card .card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 1.25rem;
}

.seeder-form-card .card-title {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.seeder-form-card .card-body {
    padding: 2rem;
}

/* Form Sections */
.form-section {
    border-bottom: 1px solid #f8f9fa;
    padding-bottom: 1.5rem;
    margin-bottom: 1.5rem;
}

.form-section:last-child {
    border-bottom: none;
    padding-bottom: 0;
    margin-bottom: 0;
}

.section-header {
    margin-bottom: 1rem;
}

.section-title {
    color: #495057;
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 0.25rem;
}

.section-title i {
    color: #6c757d;
}

/* Summary Card */
.summary-card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.summary-card .card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.summary-section {
    padding-bottom: 0.75rem;
    margin-bottom: 0.75rem;
    border-bottom: 1px solid #f8f9fa;
}

.summary-section:last-child {
    border-bottom: none;
    padding-bottom: 0;
    margin-bottom: 0;
}

.summary-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.25rem;
}

.summary-value {
    font-size: 0.95rem;
    color: #495057;
    line-height: 1.4;
}

/* Dealer Selector Styles */
.dealer-selector {
    position: relative;
}

.dealer-selector .dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 1050;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    background-color: #fff;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.175);
}

.dealer-selector .dropdown-item {
    padding: 0.75rem;
    border-bottom: 1px solid #f8f9fa;
    color: #495057;
    text-decoration: none;
}

.dealer-selector .dropdown-item:last-child {
    border-bottom: none;
}

.dealer-selector .dropdown-item:hover {
    background-color: #f8f9fa;
}

.dealer-selector .dropdown-item:focus {
    background-color: #e9ecef;
    outline: none;
}

/* Customer Selector Styles */
.customer-selector .form-select {
    border-color: #ced4da;
}

.customer-selector .form-select:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Count Input Styles */
.vehicle-count-input input[type="number"],
.driver-count-input input[type="number"] {
    font-size: 1.1rem;
    font-weight: 500;
}

.vehicle-count-input .form-label,
.driver-count-input .form-label {
    font-weight: 600;
    color: #495057;
}

/* Validation Feedback Styles */
.alert-light {
    background-color: #f8f9fa;
    border-color: #f8f9fa;
    color: #6c757d;
}

.validation-icon {
    width: 1.5rem;
    height: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Progress Tracker Styles */
#progressTracker {
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

#progressTracker .card-header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
}

#progressTracker .progress {
    height: 1.5rem;
}

#progressTracker .progress-bar {
    font-weight: 600;
    transition: width 0.6s ease;
}

/* Selected Item Display Styles */
.card.border-success {
    border-color: #198754 !important;
    background-color: #f8fff9;
}

.card.border-success .card-header.bg-success {
    background-color: #198754 !important;
    border-color: #198754 !important;
}

.card.border-primary {
    border-color: #0d6efd !important;
    background-color: #f8f9ff;
}

.card.border-primary .card-header.bg-primary {
    background-color: #0d6efd !important;
    border-color: #0d6efd !important;
}

/* Requirements Info Styles */
.vehicle-requirements,
.driver-requirements {
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.vehicle-requirements .alert,
.driver-requirements .alert {
    border-left: 4px solid #6c757d;
    background-color: #f8f9fa;
}

.vehicle-requirements ul li,
.driver-requirements ul li {
    margin-bottom: 0.25rem;
}

/* Large Volume Warning Styles */
.alert-warning {
    border-left: 4px solid #ffc107;
}

.alert-info {
    border-left: 4px solid #0dcaf0;
}

.alert-success {
    border-left: 4px solid #198754;
}

.alert-danger {
    border-left: 4px solid #dc3545;
}

/* Form Check Styles in Warnings */
.alert .form-check {
    margin-top: 0.5rem;
}

.alert .form-check-input:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

/* Button Styles */
.btn-outline-secondary:hover {
    background-color: #6c757d;
    border-color: #6c757d;
}

.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
}

.btn-primary:disabled {
    background-color: #6c757d;
    border-color: #6c757d;
    opacity: 0.65;
}

/* Responsive Design */
@media (max-width: 991.98px) {
    .seeder-form-card .card-body {
        padding: 1.5rem;
    }
    
    .summary-card {
        margin-top: 2rem;
    }
    
    .sticky-top {
        position: static !important;
    }
}

@media (max-width: 767.98px) {
    .seeder-form-card .card-body {
        padding: 1rem;
    }
    
    .form-section {
        padding-bottom: 1rem;
        margin-bottom: 1rem;
    }
    
    .section-title {
        font-size: 1rem;
    }
    
    .dealer-selector .dropdown-menu {
        max-height: 200px;
    }
}

/* Loading Spinner Styles */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Badge Styles */
.badge {
    font-size: 0.75rem;
    font-weight: 500;
}

.badge.bg-success {
    background-color: #198754 !important;
}

.badge.bg-secondary {
    background-color: #6c757d !important;
}

/* Form Text Styles */
.form-text {
    font-size: 0.875rem;
    color: #6c757d;
}

/* Invalid Feedback Styles */
.invalid-feedback {
    display: block;
    font-size: 0.875rem;
    color: #dc3545;
}

.form-control.is-invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);
}

/* Custom Scrollbar for Dropdown */
.dealer-selector .dropdown-menu::-webkit-scrollbar {
    width: 6px;
}

.dealer-selector .dropdown-menu::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.dealer-selector .dropdown-menu::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.dealer-selector .dropdown-menu::-webkit-scrollbar-thumb:hover {
    background: #a1a1a1;
}

/* Environment Info Styles */
.alert-info .fw-bold {
    color: #0c63e4;
}

/* Ratio Warning Specific Styles */
#driver-vehicle-ratio-warning.alert-success {
    background-color: #d1e7dd;
    border-color: #a3cfbb;
    color: #0a3622;
}

#driver-vehicle-ratio-warning.alert-warning {
    background-color: #fff3cd;
    border-color: #ffecb5;
    color: #664d03;
}

/* Navigation Integration */
.nav-link.active {
    font-weight: 600;
}

/* Smooth Transitions */
.card,
.alert,
.form-control,
.btn {
    transition: all 0.15s ease-in-out;
}

/* Focus Styles */
.form-control:focus,
.form-select:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Print Styles */
@media print {
    .seeder-form-card,
    .summary-card {
        box-shadow: none;
        border: 1px solid #dee2e6;
    }
    
    .btn,
    #progressTracker {
        display: none;
    }
}
