using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using XQ360.DataMigration.Models;

namespace XQ360.DataMigration.Web.Services.Monitoring
{
    public class PerformanceMonitoringService : IPerformanceMonitoringService
    {
        private readonly ILogger<PerformanceMonitoringService> _logger;
        private readonly MigrationConfiguration _config;
        private readonly ConcurrentDictionary<Guid, MonitoringSession> _activeSessions = new();
        private readonly ConcurrentQueue<OperationMetrics> _operationHistory = new();
        private readonly ConcurrentQueue<PerformanceMetrics> _performanceHistory = new();
        private readonly ConcurrentQueue<PerformanceAlert> _activeAlerts = new();
        private readonly Timer _metricsCollectionTimer;
        private readonly object _alertLock = new object();

        private readonly PerformanceCounter? _cpuCounter;
        private readonly PerformanceCounter? _memoryCounter;
        private readonly Process _currentProcess;

        private const int MaxHistorySize = 10000;
        private const int MetricsCollectionIntervalMs = 5000; // 5 seconds
        private const double CpuThreshold = 80.0;
        private const double MemoryThreshold = 85.0;
        private const double ErrorRateThreshold = 5.0;

        public PerformanceMonitoringService(
            ILogger<PerformanceMonitoringService> logger,
            IOptions<MigrationConfiguration> config)
        {
            _logger = logger;
            _config = config.Value;
            _currentProcess = Process.GetCurrentProcess();

            try
            {
                _cpuCounter = new PerformanceCounter("Processor", "% Processor Time", "_Total");
                _memoryCounter = new PerformanceCounter("Memory", "Available MBytes");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to initialize performance counters. Will use alternative methods.");
            }

            _metricsCollectionTimer = new Timer(CollectMetrics, null, TimeSpan.Zero, TimeSpan.FromMilliseconds(MetricsCollectionIntervalMs));

            _logger.LogInformation("Performance monitoring service initialized with {IntervalMs}ms collection interval", MetricsCollectionIntervalMs);
        }

        public async Task<PerformanceMetrics> GetCurrentMetricsAsync(CancellationToken cancellationToken = default)
        {
            return await Task.Run(() =>
            {
                var metrics = new PerformanceMetrics
                {
                    Timestamp = DateTime.UtcNow,
                    CpuUsagePercent = GetCpuUsage(),
                    MemoryUsageMB = GetMemoryUsage(),
                    ActiveConnections = GetActiveConnections(),
                    CacheHitRatio = GetCacheHitRatio(),
                    AverageResponseTimeMs = GetAverageResponseTime(),
                    RequestsPerSecond = GetRequestsPerSecond(),
                    ErrorRate = GetErrorRate()
                };

                metrics.CustomMetrics["ActiveSessions"] = _activeSessions.Count;
                metrics.CustomMetrics["OperationHistoryCount"] = _operationHistory.Count;
                metrics.CustomMetrics["AlertCount"] = _activeAlerts.Count;

                return metrics;
            }, cancellationToken);
        }

        public async Task RecordOperationAsync(OperationMetrics operation, CancellationToken cancellationToken = default)
        {
            await Task.Run(() =>
            {
                _operationHistory.Enqueue(operation);

                // Maintain history size
                while (_operationHistory.Count > MaxHistorySize)
                {
                    _operationHistory.TryDequeue(out _);
                }

                // Check for performance alerts
                CheckPerformanceAlerts(operation);

                _logger.LogDebug("Recorded operation metrics: {OperationType} - {Duration}ms, {Records} records, Success: {Success}",
                    operation.OperationType, operation.Duration.TotalMilliseconds, operation.RecordsProcessed, operation.Success);
            }, cancellationToken);
        }

        public async Task<ThroughputReport> GetThroughputReportAsync(TimeSpan period, CancellationToken cancellationToken = default)
        {
            return await Task.Run(() =>
            {
                var cutoff = DateTime.UtcNow.Subtract(period);
                var relevantOperations = _operationHistory
                    .Where(op => op.StartTime >= cutoff && op.EndTime.HasValue)
                    .ToList();

                if (!relevantOperations.Any())
                {
                    return new ThroughputReport { Period = period };
                }

                var throughputs = relevantOperations.Select(op => op.ThroughputPerSecond).ToList();
                var dataPoints = relevantOperations
                    .Select(op => new ThroughputDataPoint
                    {
                        Timestamp = op.StartTime,
                        Throughput = op.ThroughputPerSecond,
                        OperationType = op.OperationType
                    })
                    .OrderBy(dp => dp.Timestamp)
                    .ToList();

                var throughputByType = relevantOperations
                    .GroupBy(op => op.OperationType)
                    .ToDictionary(g => g.Key, g => g.Average(op => op.ThroughputPerSecond));

                return new ThroughputReport
                {
                    Period = period,
                    AverageThroughput = throughputs.Average(),
                    PeakThroughput = throughputs.Max(),
                    MinThroughput = throughputs.Min(),
                    DataPoints = dataPoints,
                    ThroughputByOperationType = throughputByType
                };
            }, cancellationToken);
        }

        public async Task<List<PerformanceAlert>> GetActiveAlertsAsync(CancellationToken cancellationToken = default)
        {
            return await Task.Run(() =>
            {
                return _activeAlerts.Where(alert => alert.IsActive).ToList();
            }, cancellationToken);
        }

        public async Task StartMonitoringSessionAsync(Guid sessionId, string operationType, CancellationToken cancellationToken = default)
        {
            await Task.Run(() =>
            {
                var session = new MonitoringSession
                {
                    SessionId = sessionId,
                    OperationType = operationType,
                    StartTime = DateTime.UtcNow,
                    Metrics = new List<PerformanceMetrics>()
                };

                _activeSessions.TryAdd(sessionId, session);

                _logger.LogInformation("Started monitoring session {SessionId} for operation type {OperationType}",
                    sessionId, operationType);
            }, cancellationToken);
        }

        public async Task EndMonitoringSessionAsync(Guid sessionId, CancellationToken cancellationToken = default)
        {
            await Task.Run(() =>
            {
                if (_activeSessions.TryRemove(sessionId, out var session))
                {
                    session.EndTime = DateTime.UtcNow;
                    session.Duration = session.EndTime.Value.Subtract(session.StartTime);

                    _logger.LogInformation("Ended monitoring session {SessionId}. Duration: {Duration}, Metrics collected: {Count}",
                        sessionId, session.Duration, session.Metrics.Count);
                }
            }, cancellationToken);
        }

        public async Task<ResourceUtilizationReport> GetResourceUtilizationAsync(CancellationToken cancellationToken = default)
        {
            return await Task.Run(() =>
            {
                var report = new ResourceUtilizationReport
                {
                    Timestamp = DateTime.UtcNow,
                    Cpu = new CpuUtilization
                    {
                        CurrentPercent = GetCpuUsage(),
                        CoreCount = Environment.ProcessorCount
                    },
                    Memory = new MemoryUtilization
                    {
                        UsedMB = GetMemoryUsage(),
                        GcCollections = GC.CollectionCount(0) + GC.CollectionCount(1) + GC.CollectionCount(2),
                        GcMemoryMB = GC.GetTotalMemory(false) / (1024 * 1024)
                    },
                    Database = new DatabaseUtilization
                    {
                        AverageQueryTimeMs = GetAverageQueryTime(),
                        QueriesPerSecond = GetQueriesPerSecond()
                    }
                };

                // Calculate additional metrics from recent history
                var recentMetrics = _performanceHistory.TakeLast(20).ToList();
                if (recentMetrics.Any())
                {
                    report.Cpu.AveragePercent = recentMetrics.Average(m => m.CpuUsagePercent);
                    report.Cpu.PeakPercent = recentMetrics.Max(m => m.CpuUsagePercent);
                }

                return report;
            }, cancellationToken);
        }

        public async Task<ErrorRateReport> GetErrorRateReportAsync(TimeSpan period, CancellationToken cancellationToken = default)
        {
            return await Task.Run(() =>
            {
                var cutoff = DateTime.UtcNow.Subtract(period);
                var relevantOperations = _operationHistory
                    .Where(op => op.StartTime >= cutoff)
                    .ToList();

                if (!relevantOperations.Any())
                {
                    return new ErrorRateReport { Period = period };
                }

                var totalOperations = relevantOperations.Count;
                var errorOperations = relevantOperations.Where(op => !op.Success).ToList();
                var errorCount = errorOperations.Count;

                var overallErrorRate = totalOperations > 0 ? (double)errorCount / totalOperations * 100 : 0;

                var errorRateByType = relevantOperations
                    .GroupBy(op => op.OperationType)
                    .ToDictionary(g => g.Key, g =>
                    {
                        var typeTotal = g.Count();
                        var typeErrors = g.Count(op => !op.Success);
                        return typeTotal > 0 ? (double)typeErrors / typeTotal * 100 : 0;
                    });

                var mostCommonErrors = errorOperations
                    .Where(op => !string.IsNullOrEmpty(op.ErrorMessage))
                    .GroupBy(op => op.ErrorMessage!)
                    .OrderByDescending(g => g.Count())
                    .Take(10)
                    .Select(g => g.Key)
                    .ToList();

                return new ErrorRateReport
                {
                    Period = period,
                    OverallErrorRate = overallErrorRate,
                    ErrorRateByType = errorRateByType,
                    MostCommonErrors = mostCommonErrors
                };
            }, cancellationToken);
        }

        private void CollectMetrics(object? state)
        {
            try
            {
                var metrics = new PerformanceMetrics
                {
                    Timestamp = DateTime.UtcNow,
                    CpuUsagePercent = GetCpuUsage(),
                    MemoryUsageMB = GetMemoryUsage(),
                    ActiveConnections = GetActiveConnections(),
                    CacheHitRatio = GetCacheHitRatio(),
                    AverageResponseTimeMs = GetAverageResponseTime(),
                    RequestsPerSecond = GetRequestsPerSecond(),
                    ErrorRate = GetErrorRate()
                };

                _performanceHistory.Enqueue(metrics);

                // Maintain history size
                while (_performanceHistory.Count > MaxHistorySize)
                {
                    _performanceHistory.TryDequeue(out _);
                }

                // Add metrics to active sessions
                foreach (var session in _activeSessions.Values)
                {
                    session.Metrics.Add(metrics);
                }

                // Check for system-level alerts
                CheckSystemAlerts(metrics);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to collect performance metrics");
            }
        }

        private double GetCpuUsage()
        {
            try
            {
                if (_cpuCounter != null)
                {
                    return _cpuCounter.NextValue();
                }

                // Fallback: use process CPU time
                return _currentProcess.TotalProcessorTime.TotalMilliseconds / Environment.TickCount * 100;
            }
            catch
            {
                return 0;
            }
        }

        private long GetMemoryUsage()
        {
            try
            {
                return _currentProcess.WorkingSet64 / (1024 * 1024); // Convert to MB
            }
            catch
            {
                return 0;
            }
        }

        private int GetActiveConnections()
        {
            // This would need to be implemented based on the connection pool monitoring
            return _activeSessions.Count;
        }

        private int GetCacheHitRatio()
        {
            // This would need to be implemented based on cache service metrics
            return 85; // Default assumption
        }

        private double GetAverageResponseTime()
        {
            var recentOperations = _operationHistory.TakeLast(100).ToList();
            return recentOperations.Any() ? recentOperations.Average(op => op.Duration.TotalMilliseconds) : 0;
        }

        private int GetRequestsPerSecond()
        {
            var lastMinute = DateTime.UtcNow.AddMinutes(-1);
            var recentOperations = _operationHistory
                .Where(op => op.StartTime >= lastMinute)
                .Count();
            return recentOperations / 60;
        }

        private int GetErrorRate()
        {
            var recentOperations = _operationHistory.TakeLast(100).ToList();
            if (!recentOperations.Any()) return 0;

            var errorCount = recentOperations.Count(op => !op.Success);
            return (int)((double)errorCount / recentOperations.Count * 100);
        }

        private double GetAverageQueryTime()
        {
            var recentOperations = _operationHistory
                .Where(op => op.OperationType.Contains("SQL") || op.OperationType.Contains("Database"))
                .TakeLast(50)
                .ToList();
            return recentOperations.Any() ? recentOperations.Average(op => op.Duration.TotalMilliseconds) : 0;
        }

        private int GetQueriesPerSecond()
        {
            var lastMinute = DateTime.UtcNow.AddMinutes(-1);
            var recentQueries = _operationHistory
                .Where(op => op.StartTime >= lastMinute && 
                            (op.OperationType.Contains("SQL") || op.OperationType.Contains("Database")))
                .Count();
            return recentQueries / 60;
        }

        private void CheckPerformanceAlerts(OperationMetrics operation)
        {
            // Check for slow operations
            if (operation.Duration.TotalSeconds > 30) // 30 seconds threshold
            {
                CreateAlert(AlertType.SlowResponse, AlertSeverity.Warning,
                    $"Slow operation detected: {operation.OperationType} took {operation.Duration.TotalSeconds:F1} seconds",
                    new Dictionary<string, object>
                    {
                        ["OperationType"] = operation.OperationType,
                        ["Duration"] = operation.Duration.TotalSeconds,
                        ["SessionId"] = operation.SessionId
                    });
            }

            // Check for low throughput
            if (operation.RecordsProcessed > 100 && operation.ThroughputPerSecond < 10)
            {
                CreateAlert(AlertType.LowThroughput, AlertSeverity.Warning,
                    $"Low throughput detected: {operation.ThroughputPerSecond:F1} records/second",
                    new Dictionary<string, object>
                    {
                        ["OperationType"] = operation.OperationType,
                        ["Throughput"] = operation.ThroughputPerSecond,
                        ["RecordsProcessed"] = operation.RecordsProcessed
                    });
            }
        }

        private void CheckSystemAlerts(PerformanceMetrics metrics)
        {
            // Check CPU usage
            if (metrics.CpuUsagePercent > CpuThreshold)
            {
                CreateAlert(AlertType.HighCpuUsage, AlertSeverity.Warning,
                    $"High CPU usage detected: {metrics.CpuUsagePercent:F1}%",
                    new Dictionary<string, object> { ["CpuUsage"] = metrics.CpuUsagePercent });
            }

            // Check memory usage
            if (metrics.MemoryUsageMB > 1500) // 1.5GB threshold
            {
                CreateAlert(AlertType.HighMemoryUsage, AlertSeverity.Warning,
                    $"High memory usage detected: {metrics.MemoryUsageMB}MB",
                    new Dictionary<string, object> { ["MemoryUsage"] = metrics.MemoryUsageMB });
            }

            // Check error rate
            if (metrics.ErrorRate > ErrorRateThreshold)
            {
                CreateAlert(AlertType.HighErrorRate, AlertSeverity.Critical,
                    $"High error rate detected: {metrics.ErrorRate}%",
                    new Dictionary<string, object> { ["ErrorRate"] = metrics.ErrorRate });
            }
        }

        private void CreateAlert(AlertType type, AlertSeverity severity, string message, Dictionary<string, object> context)
        {
            lock (_alertLock)
            {
                // Check if similar alert already exists
                var existingAlert = _activeAlerts.FirstOrDefault(a => 
                    a.Type == type && a.IsActive && 
                    DateTime.UtcNow.Subtract(a.Timestamp).TotalMinutes < 5);

                if (existingAlert != null)
                    return; // Don't create duplicate alerts within 5 minutes

                var alert = new PerformanceAlert
                {
                    AlertId = Guid.NewGuid(),
                    Type = type,
                    Severity = severity,
                    Message = message,
                    Timestamp = DateTime.UtcNow,
                    IsActive = true,
                    Context = context
                };

                _activeAlerts.Enqueue(alert);

                // Maintain alert history size
                while (_activeAlerts.Count > 1000)
                {
                    _activeAlerts.TryDequeue(out _);
                }

                _logger.LogWarning("Performance alert created: {Type} - {Message}", type, message);
            }
        }

        public void Dispose()
        {
            _metricsCollectionTimer?.Dispose();
            _cpuCounter?.Dispose();
            _memoryCounter?.Dispose();
            _currentProcess?.Dispose();
        }

        private class MonitoringSession
        {
            public Guid SessionId { get; set; }
            public string OperationType { get; set; } = string.Empty;
            public DateTime StartTime { get; set; }
            public DateTime? EndTime { get; set; }
            public TimeSpan Duration { get; set; }
            public List<PerformanceMetrics> Metrics { get; set; } = new();
        }
    }
}
