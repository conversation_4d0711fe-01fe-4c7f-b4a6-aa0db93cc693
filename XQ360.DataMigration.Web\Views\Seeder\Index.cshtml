@model XQ360.DataMigration.Web.Models.BulkSeederViewModel
@{
    ViewData["Title"] = "Data Seeder";
}

<div class="container-fluid py-4">
    <div class="row">
        <!-- Main Form Column -->
        <div class="col-lg-8">
            <!-- Import Progress Tracker (shown when import is active) -->
            @if (!string.IsNullOrEmpty(Model.ActiveSessionId))
            {
                <div class="mb-4">
                    <div id="progressTracker" class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-spinner fa-spin me-2"></i>
                                Seeding Operation in Progress
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="progress mb-3">
                                <div id="progressBar" class="progress-bar progress-bar-striped progress-bar-animated" 
                                     role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                                    0%
                                </div>
                            </div>
                            <div id="progressStatus" class="text-muted">Initializing...</div>
                            <div class="mt-3">
                                <button type="button" class="btn btn-outline-danger btn-sm" onclick="cancelSeeding()">
                                    <i class="fas fa-stop me-1"></i>
                                    Cancel Operation
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            }

            <!-- Single-Page Seeder Form -->
            <div class="card minimal-card seeder-form-card">
                <div class="card-header">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-database me-2"></i>
                        Bulk Data Seeder Configuration
                    </h4>
                    <p class="text-muted mb-0">Configure your bulk data seeding operation by filling out all required fields below</p>
                </div>
                
                <div class="card-body">
                    <form id="seederForm" asp-action="CreateSession" asp-controller="Seeder" method="post">
                        <!-- Environment Information (Read-only, uses existing system) -->
                        <div class="form-section mb-4">
                            <div class="section-header mb-3">
                                <h5 class="section-title">
                                    <i class="fas fa-server me-2"></i>
                                    Environment
                                </h5>
                                <p class="text-muted mb-0">Current target environment for your seeding operation</p>
                            </div>

                            <div class="alert alert-info">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <div>
                                        <strong>Current Environment: @Model.CurrentEnvironment</strong>
                                        <br>
                                        <small class="text-muted">Environment selection is managed through the main application settings</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Dealer Selection Section -->
                        <div class="form-section mb-4">
                            <div class="section-header mb-3">
                                <h5 class="section-title">
                                    <i class="fas fa-building me-2"></i>
                                    Dealer
                                </h5>
                                <p class="text-muted mb-0">Select an existing dealer from the system</p>
                            </div>

                            @await Html.PartialAsync("_DealerSelector", Model)
                        </div>

                        <!-- Customer Selection Section -->
                        <div class="form-section mb-4">
                            <div class="section-header mb-3">
                                <h5 class="section-title">
                                    <i class="fas fa-users me-2"></i>
                                    Customer
                                </h5>
                                <p class="text-muted mb-0">Select an existing customer or create a new one</p>
                            </div>

                            @await Html.PartialAsync("_CustomerSelector", Model)
                        </div>

                        <!-- Vehicle Count Section -->
                        <div class="form-section mb-4">
                            <div class="section-header mb-3">
                                <h5 class="section-title">
                                    <i class="fas fa-car me-2"></i>
                                    Vehicle Count
                                </h5>
                                <p class="text-muted mb-0">Number of vehicles to generate for seeding</p>
                            </div>

                            @await Html.PartialAsync("_VehicleCountInput", Model)
                        </div>

                        <!-- Driver Count Section -->
                        <div class="form-section mb-4">
                            <div class="section-header mb-3">
                                <h5 class="section-title">
                                    <i class="fas fa-id-card me-2"></i>
                                    Driver Count
                                </h5>
                                <p class="text-muted mb-0">Number of drivers to generate for seeding</p>
                            </div>

                            @await Html.PartialAsync("_DriverCountInput", Model)
                        </div>

                        <!-- Form Actions -->
                        <div class="form-section">
                            <div class="d-flex justify-content-between align-items-center">
                                <button type="button" class="btn btn-outline-secondary" onclick="validateConfiguration()">
                                    <i class="fas fa-check-circle me-1"></i>
                                    Validate Configuration
                                </button>
                                
                                <button type="submit" class="btn btn-primary" id="startSeedingBtn" disabled>
                                    <i class="fas fa-play me-1"></i>
                                    Start Seeding Operation
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Configuration Summary Sidebar -->
        <div class="col-lg-4">
            <div class="sticky-top" style="top: 1rem;">
                <!-- Configuration Summary -->
                <div class="card summary-card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-list-ul me-2"></i>
                            Configuration Summary
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="configurationSummary">
                            <!-- Complete Configuration -->
                            <div id="completeSummary" style="display: none;">
                                <div class="d-flex align-items-center mb-3">
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    <div>
                                        <div class="fw-bold">Ready to Seed</div>
                                        <div class="small text-muted">All required fields completed</div>
                                    </div>
                                </div>
                                
                                <div class="summary-section mb-3">
                                    <h6 class="summary-label">Environment</h6>
                                    <div class="summary-value" id="summaryEnvironment">@Model.CurrentEnvironment</div>
                                </div>
                                
                                <div class="summary-section mb-3">
                                    <h6 class="summary-label">Dealer</h6>
                                    <div class="summary-value" id="summaryDealer">Not selected</div>
                                </div>
                                
                                <div class="summary-section mb-3">
                                    <h6 class="summary-label">Customer</h6>
                                    <div class="summary-value" id="summaryCustomer">Not selected</div>
                                </div>
                                
                                <div class="summary-section mb-3">
                                    <h6 class="summary-label">Data to Generate</h6>
                                    <div class="summary-value">
                                        <div id="summaryVehicles">0 vehicles</div>
                                        <div id="summaryDrivers">0 drivers</div>
                                    </div>
                                </div>
                                
                                <div class="summary-section mb-3">
                                    <h6 class="summary-label">Estimated Time</h6>
                                    <div class="summary-value" id="summaryTime">Calculating...</div>
                                </div>

                                <!-- Validation Results -->
                                <div id="validationResults" style="display: none;">
                                    <div class="summary-section">
                                        <h6 class="summary-label">Pre-Validation</h6>
                                        <div id="validationStatus" class="summary-value"></div>
                                    </div>
                                </div>
                            </div>

                            <!-- Incomplete Form -->
                            <div id="incompleteSummary">
                                <div class="alert alert-light">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-info-circle me-2"></i>
                                        <div>
                                            <div class="fw-bold">Complete Configuration</div>
                                            <div class="small text-muted">Fill out all required fields to validate</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="~/js/seeder-wizard.js" asp-append-version="true"></script>
    <script>
        $(document).ready(function() {
            // Initialize seeder wizard
            SeederWizard.init({
                sessionId: '@Model.ActiveSessionId',
                isImporting: @Html.Raw(Model.IsImporting.ToString().ToLower()),
                currentEnvironment: '@Model.CurrentEnvironment'
            });
        });
    </script>
}
